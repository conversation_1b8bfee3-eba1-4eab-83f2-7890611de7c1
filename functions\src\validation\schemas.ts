import { z } from 'zod';

// Security-focused validation schemas for Hive Campus

// Admin PIN validation - now requires 12 digits minimum
export const AdminPinSchema = z.object({
  pin: z.string()
    .regex(/^\d{12,}$/, 'PIN must be at least 12 digits')
    .min(12, 'PIN must be at least 12 digits')
    .max(20, 'PIN cannot exceed 20 digits')
});

// Listing validation schemas
export const ListingTitleSchema = z.string()
  .min(1, 'Title is required')
  .max(80, 'Title cannot exceed 80 characters')
  .regex(/^[^<>]*$/, 'Title cannot contain HTML tags')
  .trim();

export const ListingDescriptionSchema = z.string()
  .max(500, 'Description cannot exceed 500 characters')
  .regex(/^(?!.*<script).*$/i, 'Description cannot contain script tags')
  .trim();

export const ListingPriceSchema = z.number()
  .positive('Price must be positive')
  .max(10000, 'Price cannot exceed $10,000')
  .multipleOf(0.01, 'Price must be in cents');

export const ListingCreateSchema = z.object({
  title: ListingTitleSchema,
  description: ListingDescriptionSchema,
  price: ListingPriceSchema,
  category: z.enum(['textbooks', 'electronics', 'furniture', 'clothing', 'other']),
  condition: z.enum(['new', 'like_new', 'good', 'fair', 'poor']),
  university: z.string().min(1, 'University is required'),
  visibility: z.enum(['public', 'university']).default('university')
});

// Message validation schemas
export const MessageTextSchema = z.string()
  .min(1, 'Message cannot be empty')
  .max(1000, 'Message cannot exceed 1000 characters')
  .regex(/^(?!.*<script).*$/i, 'Message cannot contain script tags')
  .trim();

export const ChatMessageSchema = z.object({
  text: MessageTextSchema,
  receiverId: z.string().min(1, 'Receiver ID is required'),
  chatId: z.string().min(1, 'Chat ID is required')
});

// Order validation schemas
export const OrderIdSchema = z.string()
  .min(1, 'Order ID is required')
  .regex(/^[a-zA-Z0-9_-]+$/, 'Invalid order ID format');

export const SecretCodeSchema = z.string()
  .regex(/^[a-fA-F0-9]{6}$/, 'Secret code must be 6 hexadecimal characters');

export const ReleaseFundsSchema = z.object({
  orderId: OrderIdSchema,
  secretCode: SecretCodeSchema
});

// Payment validation schemas
export const CheckoutSessionSchema = z.object({
  listingId: z.string().min(1, 'Listing ID is required'),
  quantity: z.number().int().positive().max(10).default(1),
  useWalletBalance: z.boolean().default(false),
  orderDetails: z.object({
    price: ListingPriceSchema.optional(),
    shippingFee: z.number().min(0).max(100).default(0),
    appliedWalletCredit: z.number().min(0).default(0)
  }).optional()
});

// Wallet validation schemas
export const WalletAmountSchema = z.number()
  .positive('Amount must be positive')
  .max(1000, 'Amount cannot exceed $1000')
  .multipleOf(0.01, 'Amount must be in cents');

export const WalletSettingsSchema = z.object({
  signupBonus: z.number().min(0).max(100),
  referralBonus: z.number().min(0).max(100),
  enableSignupBonus: z.boolean(),
  enableReferralBonus: z.boolean()
});

// User validation schemas
export const UserEmailSchema = z.string()
  .email('Invalid email format')
  .regex(/\.(edu|ac\.|edu\.)/, 'Must be an educational email address');

export const UserNameSchema = z.string()
  .min(1, 'Name is required')
  .max(50, 'Name cannot exceed 50 characters')
  .regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes')
  .trim();

// Shipping validation schemas
export const AddressSchema = z.object({
  name: UserNameSchema,
  street1: z.string().min(1, 'Street address is required').max(100),
  street2: z.string().max(100).optional(),
  city: z.string().min(1, 'City is required').max(50),
  state: z.string().min(2, 'State is required').max(2),
  zip: z.string().regex(/^\d{5}(-\d{4})?$/, 'Invalid ZIP code format'),
  country: z.string().length(2, 'Country must be 2-letter code').default('US')
});

// Admin notification validation
export const AdminNotificationSchema = z.object({
  type: z.enum(['user_signup', 'payment_completed', 'payment_failed', 'order_created', 'listing_created']),
  title: z.string().min(1).max(100),
  message: z.string().min(1).max(500),
  userId: z.string().optional(),
  username: z.string().optional(),
  orderId: z.string().optional(),
  amount: z.number().optional(),
  metadata: z.record(z.unknown()).default({}),
  actionUrl: z.string().url().optional()
});

// Rate limiting schemas
export const RateLimitSchema = z.object({
  identifier: z.string().min(1), // IP or user ID
  action: z.string().min(1),     // Action type (login, pin_verify, etc.)
  windowMs: z.number().positive().default(15 * 60 * 1000), // 15 minutes
  maxAttempts: z.number().positive().default(5)
});

// Helper function to validate and sanitize input
export const validateInput = <T>(schema: z.ZodSchema<T>, data: unknown): T => {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const messages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      throw new Error(`Validation failed: ${messages.join(', ')}`);
    }
    throw error;
  }
};

// Export all schemas for easy import
export const ValidationSchemas = {
  AdminPin: AdminPinSchema,
  ListingTitle: ListingTitleSchema,
  ListingDescription: ListingDescriptionSchema,
  ListingPrice: ListingPriceSchema,
  ListingCreate: ListingCreateSchema,
  MessageText: MessageTextSchema,
  ChatMessage: ChatMessageSchema,
  OrderId: OrderIdSchema,
  SecretCode: SecretCodeSchema,
  ReleaseFunds: ReleaseFundsSchema,
  CheckoutSession: CheckoutSessionSchema,
  WalletAmount: WalletAmountSchema,
  WalletSettings: WalletSettingsSchema,
  UserEmail: UserEmailSchema,
  UserName: UserNameSchema,
  Address: AddressSchema,
  AdminNotification: AdminNotificationSchema,
  RateLimit: RateLimitSchema
};

import * as admin from 'firebase-admin';
import * as functions from 'firebase-functions/v1';
import { validateInput, WalletAmountSchema } from '../validation/schemas';

// Secure wallet transaction utilities for Hive Campus

interface WalletTransaction {
  userId: string;
  amount: number;
  type: 'credit' | 'debit';
  description: string;
  orderId?: string;
  metadata?: Record<string, unknown>;
}

interface WalletBalance {
  balance: number;
  lastUpdated: admin.firestore.Timestamp;
  version: number; // For optimistic locking
}

/**
 * Secure wallet operations with atomic transactions
 */
export class SecureWalletManager {
  /**
   * Atomically deduct amount from wallet with race condition protection
   */
  static async deductFromWallet(
    userId: string,
    amount: number,
    orderId: string,
    description: string = 'Order payment'
  ): Promise<{ success: boolean; newBalance: number; transactionId: string }> {
    // Validate input
    const validatedAmount = validateInput(WalletAmountSchema, amount);
    
    if (!userId || !orderId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'User ID and Order ID are required'
      );
    }

    const walletRef = admin.firestore().collection('wallets').doc(userId);
    const transactionRef = admin.firestore().collection('walletTransactions').doc();
    
    try {
      const result = await admin.firestore().runTransaction(async (transaction) => {
        // Get current wallet state
        const walletDoc = await transaction.get(walletRef);
        
        if (!walletDoc.exists) {
          throw new functions.https.HttpsError(
            'not-found',
            'Wallet not found for user'
          );
        }
        
        const walletData = walletDoc.data() as WalletBalance;
        const currentBalance = walletData.balance || 0;
        
        // Check if sufficient balance
        if (currentBalance < validatedAmount) {
          throw new functions.https.HttpsError(
            'failed-precondition',
            `Insufficient wallet balance. Available: $${currentBalance.toFixed(2)}, Required: $${validatedAmount.toFixed(2)}`
          );
        }
        
        // Check for duplicate transaction (idempotency)
        const existingTransactionQuery = await admin.firestore()
          .collection('walletTransactions')
          .where('userId', '==', userId)
          .where('orderId', '==', orderId)
          .where('type', '==', 'debit')
          .limit(1)
          .get();
        
        if (!existingTransactionQuery.empty) {
          throw new functions.https.HttpsError(
            'already-exists',
            'Transaction already processed for this order'
          );
        }
        
        const newBalance = currentBalance - validatedAmount;
        const now = admin.firestore.Timestamp.now();
        
        // Update wallet balance with version increment for optimistic locking
        transaction.update(walletRef, {
          balance: newBalance,
          lastUpdated: now,
          version: admin.firestore.FieldValue.increment(1)
        });
        
        // Create transaction record
        const transactionData: WalletTransaction & {
          id: string;
          createdAt: admin.firestore.Timestamp;
          status: 'completed';
          orderId: string;
        } = {
          id: transactionRef.id,
          userId,
          amount: validatedAmount,
          type: 'debit',
          description,
          orderId,
          createdAt: now,
          status: 'completed',
          metadata: {
            previousBalance: currentBalance,
            newBalance,
            source: 'order_payment'
          }
        };
        
        transaction.set(transactionRef, transactionData);
        
        console.log(`✅ Wallet deduction successful: User ${userId}, Amount $${validatedAmount}, New Balance $${newBalance}`);
        
        return {
          success: true,
          newBalance,
          transactionId: transactionRef.id
        };
      });
      
      return result;
    } catch (error) {
      console.error('❌ Wallet deduction failed:', error);
      
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      
      throw new functions.https.HttpsError(
        'internal',
        'Failed to process wallet deduction'
      );
    }
  }

  /**
   * Atomically add amount to wallet (for refunds, bonuses, etc.)
   */
  static async creditWallet(
    userId: string,
    amount: number,
    description: string,
    orderId?: string,
    metadata?: Record<string, unknown>
  ): Promise<{ success: boolean; newBalance: number; transactionId: string }> {
    // Validate input
    const validatedAmount = validateInput(WalletAmountSchema, amount);
    
    if (!userId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'User ID is required'
      );
    }

    const walletRef = admin.firestore().collection('wallets').doc(userId);
    const transactionRef = admin.firestore().collection('walletTransactions').doc();
    
    try {
      const result = await admin.firestore().runTransaction(async (transaction) => {
        // Get or create wallet
        const walletDoc = await transaction.get(walletRef);
        
        let currentBalance = 0;
        let walletExists = walletDoc.exists;
        
        if (walletExists) {
          const walletData = walletDoc.data() as WalletBalance;
          currentBalance = walletData.balance || 0;
        }
        
        const newBalance = currentBalance + validatedAmount;
        const now = admin.firestore.Timestamp.now();
        
        // Prevent excessive wallet balances (security measure)
        if (newBalance > 10000) { // $10,000 limit
          throw new functions.https.HttpsError(
            'failed-precondition',
            'Wallet balance would exceed maximum allowed limit'
          );
        }
        
        // Update or create wallet
        const walletData = {
          userId,
          balance: newBalance,
          lastUpdated: now,
          version: walletExists ? admin.firestore.FieldValue.increment(1) : 1
        };
        
        if (walletExists) {
          transaction.update(walletRef, walletData);
        } else {
          transaction.set(walletRef, {
            ...walletData,
            createdAt: now,
            referralCode: `user${userId.substring(0, 6)}`,
            usedReferral: false
          });
        }
        
        // Create transaction record
        const transactionData: WalletTransaction & {
          id: string;
          createdAt: admin.firestore.Timestamp;
          status: 'completed';
        } = {
          id: transactionRef.id,
          userId,
          amount: validatedAmount,
          type: 'credit',
          description,
          orderId,
          createdAt: now,
          status: 'completed',
          metadata: {
            previousBalance: currentBalance,
            newBalance,
            ...metadata
          }
        };
        
        transaction.set(transactionRef, transactionData);
        
        console.log(`✅ Wallet credit successful: User ${userId}, Amount $${validatedAmount}, New Balance $${newBalance}`);
        
        return {
          success: true,
          newBalance,
          transactionId: transactionRef.id
        };
      });
      
      return result;
    } catch (error) {
      console.error('❌ Wallet credit failed:', error);
      
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      
      throw new functions.https.HttpsError(
        'internal',
        'Failed to process wallet credit'
      );
    }
  }

  /**
   * Get wallet balance securely
   */
  static async getWalletBalance(userId: string): Promise<number> {
    if (!userId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'User ID is required'
      );
    }

    try {
      const walletDoc = await admin.firestore()
        .collection('wallets')
        .doc(userId)
        .get();
      
      if (!walletDoc.exists) {
        return 0;
      }
      
      const walletData = walletDoc.data() as WalletBalance;
      return walletData.balance || 0;
    } catch (error) {
      console.error('❌ Error getting wallet balance:', error);
      throw new functions.https.HttpsError(
        'internal',
        'Failed to get wallet balance'
      );
    }
  }

  /**
   * Validate wallet transaction for security
   */
  static validateTransaction(
    amount: number,
    userId: string,
    orderId?: string
  ): void {
    // Validate amount
    if (amount <= 0 || amount > 1000) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Transaction amount must be between $0.01 and $1000'
      );
    }

    // Validate user ID format
    if (!userId || userId.length < 10 || !/^[a-zA-Z0-9]+$/.test(userId)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Invalid user ID format'
      );
    }

    // Validate order ID format if provided
    if (orderId && (orderId.length < 5 || !/^[a-zA-Z0-9_-]+$/.test(orderId))) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Invalid order ID format'
      );
    }
  }

  /**
   * Get transaction history for a user (with pagination)
   */
  static async getTransactionHistory(
    userId: string,
    limit: number = 20,
    startAfter?: string
  ): Promise<{
    transactions: any[];
    hasMore: boolean;
    lastVisible?: string;
  }> {
    if (!userId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'User ID is required'
      );
    }

    try {
      let query = admin.firestore()
        .collection('walletTransactions')
        .where('userId', '==', userId)
        .orderBy('createdAt', 'desc')
        .limit(limit + 1); // Get one extra to check if there are more

      if (startAfter) {
        const startAfterDoc = await admin.firestore()
          .collection('walletTransactions')
          .doc(startAfter)
          .get();
        
        if (startAfterDoc.exists) {
          query = query.startAfter(startAfterDoc);
        }
      }

      const snapshot = await query.get();
      const transactions = snapshot.docs.slice(0, limit).map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      const hasMore = snapshot.docs.length > limit;
      const lastVisible = transactions.length > 0 ? transactions[transactions.length - 1].id : undefined;

      return {
        transactions,
        hasMore,
        lastVisible
      };
    } catch (error) {
      console.error('❌ Error getting transaction history:', error);
      throw new functions.https.HttpsError(
        'internal',
        'Failed to get transaction history'
      );
    }
  }
}

// Export wallet configuration
export const WalletConfig = {
  MAX_BALANCE: 10000,
  MIN_TRANSACTION: 0.01,
  MAX_TRANSACTION: 1000,
  TRANSACTION_TIMEOUT: 30000 // 30 seconds
};

/**
 * 🏭 TEST DATA FACTORY
 * Generates consistent test data for marketplace flow testing
 */

export class TestDataFactory {
  /**
   * Generate complete test data set for all scenarios
   */
  static generateTestData() {
    return {
      // Test scenario data matching the provided JSON structure
      scenarios: {
        create_listing_shipping_enabled: {
          name: "01_create_listing_shipping_enabled",
          step: "create_listing",
          data: {
            title: "MacBook Air M1 2020",
            description: "Used for 1 semester. Perfect condition. Original charger included.",
            price: 550,
            category: "Electronics",
            condition: "Used - Like New",
            quantity: 1,
            images: ["macbook_air.jpg"],
            shipping_enabled: true,
            delivery_methods: ["Ship", "In-Person"],
            location: "Mississippi State University",
            tags: ["macbook", "apple", "laptop"]
          },
          expect: "listing_created_successfully"
        },

        checkout_wallet_and_stripe: {
          name: "02_checkout_wallet_and_stripe",
          step: "buyer_checkout",
          data: {
            wallet_credit_used: 50,
            stripe_payment: 500,
            payment_method: "hybrid",
            shipping_method: "Ship",
            shipping_address: {
              name: "Buyer User",
              address_line1: "123 College Dr",
              city: "Starkville",
              state: "MS",
              zip: "39759",
              country: "US"
            }
          },
          expect: "order_created_in_escrow"
        },

        generate_shipping_label: {
          name: "03_generate_shipping_label",
          step: "seller_generates_shipping_label",
          integration: "shippo",
          action: "generate_label",
          expect: "tracking_number_attached"
        },

        mark_item_shipped: {
          name: "04_mark_item_shipped",
          step: "seller_marks_shipped",
          data: {
            tracking_number: "auto_from_shippo"
          },
          expect: "order_status_updated_to_shipped"
        },

        buyer_confirms_delivery_with_pin: {
          name: "05_buyer_confirms_delivery_with_pin",
          step: "buyer_confirms_with_pin",
          data: {
            entered_pin: "valid_generated_pin"
          },
          expect: "funds_released_to_seller"
        },

        inperson_delivery_flow: {
          name: "06_inperson_delivery_flow",
          step: "buyer_checkout_inperson",
          data: {
            delivery_method: "In-Person",
            price: 20,
            wallet_credit_used: 10,
            stripe_payment: 10
          },
          expect: "order_created_without_shipping"
        },

        inperson_meeting_and_pin_entry: {
          name: "07_inperson_meeting_and_pin_entry",
          step: "manual_pin_exchange",
          data: {
            seller_hands_item_inperson: true,
            buyer_enters_pin: "valid_generated_pin"
          },
          expect: "order_completed_and_seller_paid"
        },

        mail_in_return_flow: {
          name: "08_mail_in_return_flow",
          step: "buyer_requests_return",
          conditions: {
            buyer_marked_item_undelivered_or_damaged: true,
            request_within_return_window: true
          },
          expect: "return_approved_and_label_sent"
        },

        generate_return_label: {
          name: "09_generate_return_label",
          step: "system_creates_return_label",
          integration: "shippo",
          data: {
            shipping_direction: "buyer_to_seller"
          },
          expect: "label_sent_to_buyer"
        },

        refund_after_return_received: {
          name: "10_refund_after_return_received",
          step: "seller_confirms_return",
          action: "verify_item_condition",
          outcome: "approve_refund",
          expect: "buyer_refunded_and_order_closed"
        },

        final_validation: {
          name: "11_final_validation",
          step: "audit_analytics_and_security",
          data: {
            verify_seller_payout: true,
            buyer_order_history_updated: true,
            admin_audit_logs_present: true,
            "6_digit_pin_logged_and_secure": true
          },
          expect: "system_integrity_passed"
        }
      },

      // Test user data
      users: {
        seller: {
          email: "<EMAIL>",
          password: "TestSeller123!",
          displayName: "Test Seller",
          role: "seller",
          profile: {
            university: "Mississippi State University",
            verified: true,
            rating: 4.8,
            totalSales: 0
          }
        },
        buyer: {
          email: "<EMAIL>",
          password: "TestBuyer123!",
          displayName: "Test Buyer",
          role: "buyer",
          profile: {
            university: "Mississippi State University",
            verified: true,
            rating: 4.9,
            totalPurchases: 0
          }
        },
        admin: {
          email: "<EMAIL>",
          password: "TestAdmin123!",
          displayName: "Test Admin",
          role: "admin",
          profile: {
            permissions: ["manage_users", "manage_orders", "view_analytics"],
            verified: true
          }
        }
      },

      // Commission structure for testing
      commission: {
        flatFeeThreshold: 5.00,
        flatFeeAmount: 0.50,
        textbookCommissionRate: 0.08, // 8%
        otherItemsCommissionRate: 0.10, // 10%
        cashbackRate: 0.02 // 2%
      },

      // Shipping test data
      shipping: {
        addresses: {
          seller: {
            name: "Test Seller",
            company: "",
            street1: "456 University Ave",
            street2: "",
            city: "Starkville",
            state: "MS",
            zip: "39759",
            country: "US",
            phone: "************"
          },
          buyer: {
            name: "Test Buyer",
            company: "",
            street1: "123 College Dr",
            street2: "Apt 2B",
            city: "Starkville",
            state: "MS",
            zip: "39759",
            country: "US",
            phone: "************"
          }
        },
        package: {
          length: 12,
          width: 9,
          height: 3,
          weight: 2.5,
          distance_unit: "in",
          mass_unit: "lb"
        }
      },

      // Timing configurations
      timing: {
        autoReleaseDelayDays: 3,
        returnWindowDays: 7,
        secretCodeExpiryDays: 30
      },

      // Test payment data
      payments: {
        testCards: {
          visa: "****************",
          visaDebit: "****************",
          mastercard: "****************",
          amex: "***************",
          declined: "****************"
        },
        webhookEvents: {
          paymentSucceeded: "checkout.session.completed",
          paymentFailed: "checkout.session.async_payment_failed",
          paymentRequiresAction: "checkout.session.async_payment_requires_action"
        }
      }
    };
  }

  /**
   * Generate random test data for specific scenarios
   */
  static generateRandomTestData() {
    const randomId = Math.random().toString(36).substring(2, 15);
    
    return {
      listing: {
        title: `Test Item ${randomId}`,
        description: `Test description for item ${randomId}`,
        price: Math.floor(Math.random() * 500) + 10,
        category: ["Electronics", "Textbooks", "Clothing", "Other"][Math.floor(Math.random() * 4)],
        condition: ["New", "Used - Like New", "Used - Good", "Used - Fair"][Math.floor(Math.random() * 4)]
      },
      secretCode: Math.floor(100000 + Math.random() * 900000).toString(),
      orderId: `test_order_${randomId}`,
      trackingNumber: `1Z999AA1${randomId.substring(0, 10).toUpperCase()}`
    };
  }

  /**
   * Calculate expected commission for testing
   */
  static calculateExpectedCommission(price, category) {
    const data = this.generateTestData();
    const { flatFeeThreshold, flatFeeAmount, textbookCommissionRate, otherItemsCommissionRate } = data.commission;
    
    if (price <= flatFeeThreshold) {
      return flatFeeAmount;
    }
    
    const rate = category.toLowerCase() === 'textbooks' ? textbookCommissionRate : otherItemsCommissionRate;
    return price * rate;
  }

  /**
   * Calculate expected cashback for testing
   */
  static calculateExpectedCashback(price) {
    const data = this.generateTestData();
    return price * data.commission.cashbackRate;
  }

  /**
   * Generate test wallet transaction
   */
  static generateWalletTransaction(userId, amount, type, description) {
    return {
      id: `txn_${Math.random().toString(36).substring(2, 15)}`,
      userId,
      amount,
      type, // 'credit' or 'debit'
      description,
      createdAt: new Date(),
      status: 'completed'
    };
  }
}

# 🏪 Marketplace E2E Test Suite

## Overview

This comprehensive end-to-end test suite validates the complete marketplace transaction flow for Hive Campus, covering all scenarios from listing creation to order completion, returns, and system integrity validation.

## 🎯 Test Scenarios Covered

### Core Transaction Flow
1. **📦 Create Listing with Shipping** - Validates listing creation with shipping configuration
2. **💳 Hybrid Wallet + Stripe Checkout** - Tests wallet credit + Stripe payment processing
3. **🚚 Generate Shipping Label** - Validates Shippo integration for label generation
4. **📮 Mark Item Shipped** - Tests order status updates and tracking
5. **🔐 PIN-based Fund Release** - Validates secure fund release with 6-digit codes

### Alternative Flows
6. **🤝 In-Person Delivery** - Tests local pickup/delivery without shipping
7. **🤝 In-Person PIN Exchange** - Validates face-to-face transaction completion

### Return & Refund System
8. **↩️ Return Request Flow** - Tests return initiation within return window
9. **📦 Return Label Generation** - Validates return shipping via Shippo
10. **💰 Refund Processing** - Tests refund after return confirmation

### System Validation
11. **🔍 Final Validation** - Comprehensive system integrity checks
12. **⏰ Auto-Release System** - Tests 3-day automatic fund release
13. **🚨 Error Handling** - Validates security and error scenarios

## 🛠️ Test Architecture

### File Structure
```
tests/
├── e2e-marketplace-flow.test.js     # Main test suite
├── helpers/
│   ├── test-data-factory.js         # Test data generation
│   └── marketplace-test-helpers.js  # Test utility functions
├── mocks/
│   └── shippo-mock.js              # Shippo API mocking
├── fixtures/
│   └── test-users.json             # Test user data
└── README-MARKETPLACE-TESTS.md     # This documentation
```

### Key Components

#### 🏭 TestDataFactory
- Generates consistent test data matching your JSON specification
- Provides commission calculation utilities
- Creates mock payment and shipping data

#### 🛠️ MarketplaceTestHelpers
- User management (create, sign in, cleanup)
- Listing operations (create, retrieve)
- Wallet operations (add balance, transactions)
- Order management (create, update, validate)
- Shipping operations (labels, tracking)
- Return processing (request, approve, refund)

#### 🚢 ShippoMock
- Complete Shippo API mocking
- Realistic shipping rates and labels
- Tracking simulation
- Return label generation

## 🚀 Running the Tests

### Prerequisites
1. **Node.js** (v16 or higher)
2. **Firebase project** configured
3. **Environment variables** set up
4. **Test dependencies** installed

### Environment Setup
```bash
# Required environment variables
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_AUTH_DOMAIN=your_auth_domain
VITE_FIREBASE_STORAGE_BUCKET=your_storage_bucket
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
```

### Quick Start
```bash
# Install dependencies
npm install

# Run marketplace tests
npm run test:marketplace

# Run with watch mode
npm run test:marketplace:watch

# Run with comprehensive test runner
node scripts/run-marketplace-tests.js
```

### Advanced Usage
```bash
# Run specific test scenarios
npx vitest tests/e2e-marketplace-flow.test.js -t "Create Listing"

# Run with coverage
npx vitest tests/e2e-marketplace-flow.test.js --coverage

# Run with detailed output
npx vitest tests/e2e-marketplace-flow.test.js --reporter=verbose
```

## 📊 Test Data Structure

### Commission Structure Testing
```javascript
{
  flatFeeThreshold: 5.00,      // Items $5 and under
  flatFeeAmount: 0.50,         // $0.50 flat fee
  textbookCommissionRate: 0.08, // 8% for textbooks
  otherItemsCommissionRate: 0.10, // 10% for other items
  cashbackRate: 0.02           // 2% cashback to buyers
}
```

### Test Scenarios Data
```javascript
{
  "01_create_listing_shipping_enabled": {
    "data": {
      "title": "MacBook Air M1 2020",
      "price": 550,
      "shipping_enabled": true,
      "delivery_methods": ["Ship", "In-Person"]
    }
  },
  "02_checkout_wallet_and_stripe": {
    "data": {
      "wallet_credit_used": 50,
      "stripe_payment": 500,
      "payment_method": "hybrid"
    }
  }
  // ... more scenarios
}
```

## 🔐 Security Testing

### PIN System Validation
- Tests 6-digit secret code generation
- Validates secure code verification
- Tests invalid PIN rejection
- Ensures audit trail logging

### Wallet Security
- Tests insufficient funds handling
- Validates atomic transactions
- Tests race condition protection
- Ensures balance consistency

### Payment Security
- Tests Stripe webhook signature verification
- Validates payment intent matching
- Tests duplicate payment prevention
- Ensures secure metadata handling

## 📈 Performance Considerations

### Test Optimization
- Parallel test execution where safe
- Efficient cleanup procedures
- Minimal Firebase operations
- Mocked external API calls

### Resource Management
- Automatic test data cleanup
- User account management
- Document deletion tracking
- Memory leak prevention

## 🐛 Debugging Tests

### Common Issues
1. **Firebase Auth Errors**
   ```bash
   # Check Firebase configuration
   cat .firebaserc
   cat firebase.json
   ```

2. **Environment Variable Issues**
   ```bash
   # Verify environment variables
   echo $VITE_FIREBASE_PROJECT_ID
   ```

3. **Test Data Conflicts**
   ```bash
   # Clean up test data manually
   npm run cleanup:mock-data
   ```

### Debug Mode
```bash
# Run with debug output
DEBUG=marketplace:* npm run test:marketplace

# Run single test with logs
npx vitest tests/e2e-marketplace-flow.test.js -t "specific test" --reporter=verbose
```

## 📋 Test Reports

### Generated Reports
- **JSON Report**: `test-reports/marketplace-e2e-report.json`
- **Coverage Report**: `coverage/` directory
- **Console Output**: Real-time test progress

### Report Contents
- Test execution summary
- Individual scenario results
- Performance metrics
- Error details and stack traces
- System integrity validation results

## 🔄 Continuous Integration

### GitHub Actions Integration
```yaml
name: Marketplace E2E Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm install
      - run: npm run test:marketplace
        env:
          VITE_FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}
          # ... other environment variables
```

## 🎯 Success Criteria

### All Tests Must Pass
- ✅ Listing creation with shipping
- ✅ Hybrid payment processing
- ✅ Shipping label generation
- ✅ Order status transitions
- ✅ PIN-based fund release
- ✅ In-person delivery flow
- ✅ Return and refund processing
- ✅ Auto-release system
- ✅ Security validations
- ✅ System integrity checks

### Performance Benchmarks
- Test suite completion: < 5 minutes
- Individual test scenarios: < 30 seconds
- API response times: < 2 seconds
- Database operations: < 1 second

## 🆘 Support

### Getting Help
1. Check the test output for specific error messages
2. Review the generated test reports
3. Verify environment configuration
4. Check Firebase console for backend errors
5. Review the test helper functions for debugging utilities

### Common Solutions
- **Authentication Issues**: Verify Firebase configuration
- **Payment Failures**: Check Stripe test keys
- **Shipping Errors**: Ensure Shippo mocks are working
- **Database Errors**: Verify Firestore rules and permissions

---

**Ready to test your marketplace?** Run `npm run test:marketplace` to get started! 🚀

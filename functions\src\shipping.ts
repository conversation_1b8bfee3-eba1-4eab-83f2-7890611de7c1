import * as admin from 'firebase-admin';
import * as functions from 'firebase-functions/v1';
import { validateInput } from './validation/schemas';
import { z } from 'zod';
import axios from 'axios';

// Secure shipping system with Shippo API integration and rate limiting

// Shipping label request schema
const ShippingLabelSchema = z.object({
  orderId: z.string().min(1, 'Order ID is required'),
  fromAddress: z.object({
    name: z.string().min(1).max(50),
    street1: z.string().min(1).max(100),
    city: z.string().min(1).max(50),
    state: z.string().length(2),
    zip: z.string().regex(/^\d{5}(-\d{4})?$/),
    country: z.string().length(2).default('US')
  }),
  toAddress: z.object({
    name: z.string().min(1).max(50),
    street1: z.string().min(1).max(100),
    city: z.string().min(1).max(50),
    state: z.string().length(2),
    zip: z.string().regex(/^\d{5}(-\d{4})?$/),
    country: z.string().length(2).default('US')
  }),
  parcel: z.object({
    length: z.number().positive().max(108), // inches
    width: z.number().positive().max(108),
    height: z.number().positive().max(108),
    weight: z.number().positive().max(150), // pounds
    distance_unit: z.enum(['in', 'cm']).default('in'),
    mass_unit: z.enum(['lb', 'kg']).default('lb')
  }),
  serviceLevel: z.enum(['ground', 'priority', 'express']).default('ground')
});

// Tracking update schema
const TrackingUpdateSchema = z.object({
  orderId: z.string().min(1, 'Order ID is required'),
  trackingNumber: z.string().min(1, 'Tracking number is required'),
  carrier: z.string().min(1, 'Carrier is required')
});

interface ShippingLabel {
  labelUrl: string;
  trackingNumber: string;
  carrier: string;
  service: string;
  cost: number;
  estimatedDelivery: string;
}

interface TrackingInfo {
  status: string;
  location: string;
  timestamp: string;
  estimatedDelivery?: string;
  events: TrackingEvent[];
}

interface TrackingEvent {
  status: string;
  location: string;
  timestamp: string;
  description: string;
}

/**
 * Secure shipping manager with rate limiting and API security
 */
export class SecureShippingManager {
  private static readonly SHIPPO_API_URL = 'https://api.goshippo.com/v1';
  private static readonly API_KEY = functions.config().shippo?.api_key || process.env.SHIPPO_API_KEY;
  private static readonly RATE_LIMIT_WINDOW = 60 * 60 * 1000; // 1 hour
  private static readonly MAX_LABELS_PER_HOUR = 10;

  /**
   * Generate shipping label with rate limiting
   */
  static async generateShippingLabel(
    sellerId: string,
    labelData: any
  ): Promise<{ success: boolean; label?: ShippingLabel; error?: string }> {
    try {
      // Validate input
      const validatedData = validateInput(ShippingLabelSchema, labelData);

      // Check rate limiting
      const canGenerate = await this.checkLabelRateLimit(sellerId);
      if (!canGenerate) {
        throw new functions.https.HttpsError(
          'resource-exhausted',
          'Label generation rate limit exceeded. Maximum 10 labels per hour.'
        );
      }

      // Verify order ownership
      const orderDoc = await admin.firestore()
        .collection('orders')
        .doc(validatedData.orderId)
        .get();

      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Order not found');
      }

      const orderData = orderDoc.data();
      if (orderData?.sellerId !== sellerId) {
        throw new functions.https.HttpsError(
          'permission-denied',
          'Not authorized to generate label for this order'
        );
      }

      // Check if label already exists
      if (orderData?.shippingLabel) {
        return {
          success: true,
          label: orderData.shippingLabel
        };
      }

      // Generate label via Shippo API
      const label = await this.callShippoAPI(validatedData);

      // Store label in order
      await admin.firestore()
        .collection('orders')
        .doc(validatedData.orderId)
        .update({
          shippingLabel: label,
          trackingNumber: label.trackingNumber,
          carrier: label.carrier,
          labelGeneratedAt: admin.firestore.Timestamp.now(),
          updatedAt: admin.firestore.Timestamp.now()
        });

      // Log label generation
      await this.logLabelGeneration(sellerId, validatedData.orderId, label);

      console.log(`📦 Shipping label generated for order ${validatedData.orderId} by seller ${sellerId}`);

      return { success: true, label };
    } catch (error) {
      console.error('❌ Error generating shipping label:', error);
      
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      
      return {
        success: false,
        error: 'Failed to generate shipping label'
      };
    }
  }

  /**
   * Update tracking information
   */
  static async updateTracking(
    orderId: string,
    trackingData: {
      trackingNumber: string;
      carrier: string;
    }
  ): Promise<{ success: boolean; trackingInfo?: TrackingInfo }> {
    try {
      // Validate input
      const validatedData = validateInput(TrackingUpdateSchema, {
        orderId,
        ...trackingData
      });

      // Get tracking info from Shippo
      const trackingInfo = await this.getTrackingInfo(
        validatedData.trackingNumber,
        validatedData.carrier
      );

      // Update order with tracking info
      await admin.firestore()
        .collection('orders')
        .doc(orderId)
        .update({
          trackingInfo,
          lastTrackingUpdate: admin.firestore.Timestamp.now(),
          updatedAt: admin.firestore.Timestamp.now()
        });

      // Check if delivered and update status
      if (trackingInfo.status === 'DELIVERED') {
        // Import here to avoid circular dependency
        const { SecureOrderStatusManager, OrderStatus } = await import('./order-status');
        
        await SecureOrderStatusManager.updateOrderStatus(
          orderId,
          OrderStatus.DELIVERED,
          'system',
          {
            reason: 'Package delivered according to tracking',
            proofOfDelivery: {
              type: 'tracking_update',
              timestamp: trackingInfo.timestamp,
              metadata: {
                carrier: validatedData.carrier,
                trackingNumber: validatedData.trackingNumber,
                location: trackingInfo.location
              }
            },
            isSystemUpdate: true
          }
        );
      }

      console.log(`📍 Tracking updated for order ${orderId}: ${trackingInfo.status}`);

      return { success: true, trackingInfo };
    } catch (error) {
      console.error('❌ Error updating tracking:', error);
      return { success: false };
    }
  }

  /**
   * Get return label for disputes
   */
  static async generateReturnLabel(
    orderId: string,
    adminId: string
  ): Promise<{ success: boolean; label?: ShippingLabel }> {
    try {
      // Verify admin authorization
      const adminDoc = await admin.firestore()
        .collection('users')
        .doc(adminId)
        .get();

      if (!adminDoc.exists || adminDoc.data()?.role !== 'admin') {
        throw new functions.https.HttpsError(
          'permission-denied',
          'Only admins can generate return labels'
        );
      }

      // Get order data
      const orderDoc = await admin.firestore()
        .collection('orders')
        .doc(orderId)
        .get();

      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Order not found');
      }

      const orderData = orderDoc.data();
      
      // Check if return label already exists
      if (orderData?.returnLabel) {
        return {
          success: true,
          label: orderData.returnLabel
        };
      }

      // Generate return label (reverse addresses)
      const returnLabelData = {
        orderId,
        fromAddress: orderData.shippingAddress, // Buyer's address
        toAddress: orderData.sellerAddress,     // Seller's address
        parcel: orderData.parcel || {
          length: 12,
          width: 9,
          height: 6,
          weight: 1,
          distance_unit: 'in',
          mass_unit: 'lb'
        },
        serviceLevel: 'ground'
      };

      const returnLabel = await this.callShippoAPI(returnLabelData);

      // Store return label
      await admin.firestore()
        .collection('orders')
        .doc(orderId)
        .update({
          returnLabel,
          returnLabelGeneratedAt: admin.firestore.Timestamp.now(),
          returnLabelGeneratedBy: adminId,
          updatedAt: admin.firestore.Timestamp.now()
        });

      console.log(`🔄 Return label generated for order ${orderId} by admin ${adminId}`);

      return { success: true, label: returnLabel };
    } catch (error) {
      console.error('❌ Error generating return label:', error);
      
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      
      return { success: false };
    }
  }

  /**
   * Check label generation rate limit
   */
  private static async checkLabelRateLimit(sellerId: string): Promise<boolean> {
    try {
      const rateLimitRef = admin.firestore()
        .collection('rateLimits')
        .doc(`shipping_${sellerId}`);

      const result = await admin.firestore().runTransaction(async (transaction) => {
        const doc = await transaction.get(rateLimitRef);
        const now = admin.firestore.Timestamp.now();
        const windowStart = now.toMillis() - this.RATE_LIMIT_WINDOW;

        if (!doc.exists) {
          transaction.set(rateLimitRef, {
            count: 1,
            windowStart: now,
            lastRequest: now
          });
          return true;
        }

        const data = doc.data();
        const lastWindowStart = data?.windowStart?.toMillis() || 0;

        // Reset if window has passed
        if (lastWindowStart < windowStart) {
          transaction.set(rateLimitRef, {
            count: 1,
            windowStart: now,
            lastRequest: now
          });
          return true;
        }

        // Check if limit exceeded
        if (data?.count >= this.MAX_LABELS_PER_HOUR) {
          return false;
        }

        // Increment count
        transaction.update(rateLimitRef, {
          count: admin.firestore.FieldValue.increment(1),
          lastRequest: now
        });

        return true;
      });

      return result;
    } catch (error) {
      console.error('❌ Error checking label rate limit:', error);
      return false; // Fail secure
    }
  }

  /**
   * Call Shippo API securely
   */
  private static async callShippoAPI(labelData: any): Promise<ShippingLabel> {
    try {
      if (!this.API_KEY) {
        throw new Error('Shippo API key not configured');
      }

      // Create shipment
      const shipmentResponse = await axios.post(
        `${this.SHIPPO_API_URL}/shipments/`,
        {
          address_from: labelData.fromAddress,
          address_to: labelData.toAddress,
          parcels: [labelData.parcel],
          async: false
        },
        {
          headers: {
            'Authorization': `ShippoToken ${this.API_KEY}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000 // 30 second timeout
        }
      );

      const shipment = shipmentResponse.data;
      
      // Find the best rate
      const rates = shipment.rates || [];
      const selectedRate = rates.find((rate: any) => 
        rate.servicelevel.token === labelData.serviceLevel
      ) || rates[0];

      if (!selectedRate) {
        throw new Error('No shipping rates available');
      }

      // Create transaction (purchase label)
      const transactionResponse = await axios.post(
        `${this.SHIPPO_API_URL}/transactions/`,
        {
          rate: selectedRate.object_id,
          label_file_type: 'PDF',
          async: false
        },
        {
          headers: {
            'Authorization': `ShippoToken ${this.API_KEY}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      const transaction = transactionResponse.data;

      if (transaction.status !== 'SUCCESS') {
        throw new Error(`Label generation failed: ${transaction.messages}`);
      }

      return {
        labelUrl: transaction.label_url,
        trackingNumber: transaction.tracking_number,
        carrier: selectedRate.provider,
        service: selectedRate.servicelevel.name,
        cost: parseFloat(selectedRate.amount),
        estimatedDelivery: selectedRate.estimated_days
      };
    } catch (error) {
      console.error('❌ Shippo API call failed:', error);
      throw new functions.https.HttpsError(
        'internal',
        'Failed to generate shipping label'
      );
    }
  }

  /**
   * Get tracking information from Shippo
   */
  private static async getTrackingInfo(
    trackingNumber: string,
    carrier: string
  ): Promise<TrackingInfo> {
    try {
      if (!this.API_KEY) {
        throw new Error('Shippo API key not configured');
      }

      const response = await axios.get(
        `${this.SHIPPO_API_URL}/tracks/${carrier}/${trackingNumber}/`,
        {
          headers: {
            'Authorization': `ShippoToken ${this.API_KEY}`
          },
          timeout: 15000
        }
      );

      const tracking = response.data;

      return {
        status: tracking.tracking_status?.status || 'UNKNOWN',
        location: tracking.tracking_status?.location?.city || 'Unknown',
        timestamp: tracking.tracking_status?.status_date || new Date().toISOString(),
        estimatedDelivery: tracking.eta,
        events: (tracking.tracking_history || []).map((event: any) => ({
          status: event.status,
          location: event.location?.city || 'Unknown',
          timestamp: event.status_date,
          description: event.status_details
        }))
      };
    } catch (error) {
      console.error('❌ Error getting tracking info:', error);
      
      // Return basic info if API fails
      return {
        status: 'UNKNOWN',
        location: 'Unknown',
        timestamp: new Date().toISOString(),
        events: []
      };
    }
  }

  /**
   * Log label generation for audit trail
   */
  private static async logLabelGeneration(
    sellerId: string,
    orderId: string,
    label: ShippingLabel
  ): Promise<void> {
    try {
      await admin.firestore().collection('shippingLogs').add({
        sellerId,
        orderId,
        action: 'label_generated',
        labelInfo: {
          trackingNumber: label.trackingNumber,
          carrier: label.carrier,
          cost: label.cost
        },
        timestamp: admin.firestore.Timestamp.now()
      });
    } catch (error) {
      console.error('❌ Error logging label generation:', error);
    }
  }

  /**
   * Validate shipping address
   */
  static validateAddress(address: any): boolean {
    try {
      const schema = z.object({
        name: z.string().min(1).max(50),
        street1: z.string().min(1).max(100),
        city: z.string().min(1).max(50),
        state: z.string().length(2),
        zip: z.string().regex(/^\d{5}(-\d{4})?$/),
        country: z.string().length(2)
      });

      schema.parse(address);
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * 🚀 END-TO-<PERSON><PERSON> MARKETPLACE TRANSACTION FLOW TEST SUITE
 * Hive Campus Complete Transaction Testing
 * 
 * Tests the complete marketplace flow:
 * - Listing creation with shipping
 * - Wallet + Stripe hybrid checkout
 * - Shippo shipping integration
 * - PIN-based fund release
 * - Return processing
 * - Auto-release system
 * - Admin audit trails
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest';
import { initializeApp } from 'firebase/app';
import { getFirestore, doc, setDoc, getDoc, updateDoc, deleteDoc, collection, addDoc } from 'firebase/firestore';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { getAuth, signInWithEmailAndPassword, createUserWithEmailAndPassword } from 'firebase/auth';

// Import test helpers
import { TestDataFactory } from './helpers/test-data-factory.js';
import { MarketplaceTestHelpers } from './helpers/marketplace-test-helpers.js';
import { ShippoMock } from './mocks/shippo-mock.js';

// Mock external services
vi.mock('stripe', () => ({
  default: vi.fn(() => ({
    checkout: {
      sessions: {
        create: vi.fn().mockResolvedValue({
          id: 'cs_test_123',
          url: 'https://checkout.stripe.com/test',
          payment_intent: 'pi_test_123'
        }),
        retrieve: vi.fn().mockResolvedValue({
          id: 'cs_test_123',
          payment_status: 'paid',
          payment_intent: 'pi_test_123'
        })
      }
    },
    webhooks: {
      constructEvent: vi.fn().mockReturnValue({
        type: 'checkout.session.completed',
        data: {
          object: {
            id: 'cs_test_123',
            payment_intent: 'pi_test_123',
            metadata: {
              orderId: 'test_order_123',
              listingId: 'test_listing_123',
              buyerId: 'test_buyer_123',
              sellerId: 'test_seller_123'
            }
          }
        }
      })
    }
  }))
}));

// Test configuration
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY || 'test-api-key',
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN || 'test-project.firebaseapp.com',
  projectId: process.env.VITE_FIREBASE_PROJECT_ID || 'test-project',
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET || 'test-project.appspot.com',
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID || '123456789',
  appId: process.env.VITE_FIREBASE_APP_ID || '1:123456789:web:abcdef'
};

let app, db, functions, auth;
let testUsers = {};
let testData = {};
let helpers;

describe('🏪 End-to-End Marketplace Transaction Flow', () => {
  beforeAll(async () => {
    // Initialize Firebase
    app = initializeApp(firebaseConfig);
    db = getFirestore(app);
    functions = getFunctions(app);
    auth = getAuth(app);
    
    // Initialize helpers
    helpers = new MarketplaceTestHelpers(db, functions, auth);
    
    // Setup Shippo mock
    ShippoMock.setup();
    
    console.log('🔧 Test environment initialized');
  });

  beforeEach(async () => {
    // Create fresh test users for each test
    testUsers = await helpers.createTestUsers();
    testData = TestDataFactory.generateTestData();
    
    console.log('🧪 Test data prepared for scenario');
  });

  afterAll(async () => {
    // Cleanup test data
    await helpers.cleanupTestData();
    ShippoMock.cleanup();
    
    console.log('🧹 Test cleanup completed');
  });

  describe('📦 Scenario 01: Create Listing with Shipping Enabled', () => {
    it('should create a listing with shipping configuration', async () => {
      const { seller } = testUsers;
      const listingData = testData.scenarios.create_listing_shipping_enabled;
      
      console.log('📝 Creating listing with shipping enabled...');
      
      // Sign in as seller
      await helpers.signInUser(seller);
      
      // Create listing
      const listingId = await helpers.createListing(listingData.data);
      
      // Verify listing was created correctly
      const listing = await helpers.getListing(listingId);
      
      expect(listing).toBeDefined();
      expect(listing.title).toBe(listingData.data.title);
      expect(listing.price).toBe(listingData.data.price);
      expect(listing.shipping_enabled).toBe(true);
      expect(listing.delivery_methods).toContain('Ship');
      expect(listing.delivery_methods).toContain('In-Person');
      expect(listing.status).toBe('active');
      
      // Store for next tests
      testData.listingId = listingId;
      
      console.log(`✅ Listing created successfully: ${listingId}`);
    });
  });

  describe('💳 Scenario 02: Hybrid Wallet + Stripe Checkout', () => {
    it('should process checkout with wallet credit and Stripe payment', async () => {
      const { buyer } = testUsers;
      const checkoutData = testData.scenarios.checkout_wallet_and_stripe;
      
      console.log('💰 Processing hybrid wallet + Stripe checkout...');
      
      // Sign in as buyer
      await helpers.signInUser(buyer);
      
      // Add wallet balance to buyer
      await helpers.addWalletBalance(buyer.uid, checkoutData.data.wallet_credit_used);
      
      // Create checkout session
      const checkoutResult = await helpers.createCheckoutSession(
        testData.listingId,
        true, // use wallet balance
        {
          appliedWalletCredit: checkoutData.data.wallet_credit_used,
          shippingAddress: checkoutData.data.shipping_address
        }
      );
      
      expect(checkoutResult.success).toBe(true);
      expect(checkoutResult.walletAmountUsed).toBe(checkoutData.data.wallet_credit_used);
      expect(checkoutResult.finalStripeAmount).toBe(checkoutData.data.stripe_payment);
      
      // Simulate Stripe webhook for payment completion
      await helpers.simulateStripeWebhook(checkoutResult.sessionId, testData.listingId);
      
      // Verify order was created in escrow
      const order = await helpers.getOrderBySessionId(checkoutResult.sessionId);
      
      expect(order).toBeDefined();
      expect(order.status).toBe('payment_completed');
      expect(order.walletAmountUsed).toBe(checkoutData.data.wallet_credit_used);
      expect(order.totalAmount).toBe(550); // Original price
      expect(order.secretCode).toBeDefined();
      expect(order.secretCode.length).toBe(6);
      
      // Store for next tests
      testData.orderId = order.id;
      testData.secretCode = order.secretCode;
      
      console.log(`✅ Order created in escrow: ${order.id}`);
    });
  });

  describe('🚚 Scenario 03: Generate Shipping Label', () => {
    it('should generate shipping label via Shippo integration', async () => {
      const { seller } = testUsers;
      
      console.log('📦 Generating shipping label...');
      
      // Sign in as seller
      await helpers.signInUser(seller);
      
      // Generate shipping label
      const labelResult = await helpers.generateShippingLabel(testData.orderId);
      
      expect(labelResult.success).toBe(true);
      expect(labelResult.label.trackingNumber).toBeDefined();
      expect(labelResult.label.labelUrl).toBeDefined();
      expect(labelResult.label.carrier).toBe('USPS');
      
      // Verify order was updated with tracking info
      const order = await helpers.getOrder(testData.orderId);
      expect(order.trackingNumber).toBe(labelResult.label.trackingNumber);
      expect(order.shippingLabel).toBeDefined();
      
      // Store tracking number for next tests
      testData.trackingNumber = labelResult.label.trackingNumber;
      
      console.log(`✅ Shipping label generated: ${labelResult.label.trackingNumber}`);
    });
  });

  describe('📮 Scenario 04: Mark Item Shipped', () => {
    it('should update order status to shipped', async () => {
      const { seller } = testUsers;
      
      console.log('🚛 Marking item as shipped...');
      
      // Sign in as seller
      await helpers.signInUser(seller);
      
      // Mark item as shipped
      await helpers.markItemShipped(testData.orderId, testData.trackingNumber);
      
      // Verify order status updated
      const order = await helpers.getOrder(testData.orderId);
      expect(order.status).toBe('shipped');
      expect(order.shippedAt).toBeDefined();
      expect(order.trackingNumber).toBe(testData.trackingNumber);
      
      console.log(`✅ Order marked as shipped: ${testData.orderId}`);
    });
  });

  describe('🔐 Scenario 05: Buyer Confirms Delivery with PIN', () => {
    it('should release funds when buyer enters correct PIN', async () => {
      const { buyer } = testUsers;
      
      console.log('🎯 Testing PIN-based fund release...');
      
      // Sign in as buyer
      await helpers.signInUser(buyer);
      
      // First mark as delivered (simulate delivery)
      await helpers.markOrderDelivered(testData.orderId);
      
      // Buyer confirms delivery with PIN
      const releaseResult = await helpers.releaseFundsWithCode(testData.orderId, testData.secretCode);
      
      expect(releaseResult.success).toBe(true);
      
      // Verify order completed and funds released
      const order = await helpers.getOrder(testData.orderId);
      expect(order.status).toBe('completed');
      expect(order.fundsReleased).toBe(true);
      expect(order.fundsReleasedAt).toBeDefined();
      expect(order.completedAt).toBeDefined();
      
      console.log(`✅ Funds released successfully for order: ${testData.orderId}`);
    });
  });

  describe('🤝 Scenario 06: In-Person Delivery Flow', () => {
    it('should handle in-person checkout without shipping', async () => {
      const { buyer, seller } = testUsers;
      const inPersonData = testData.scenarios.inperson_delivery_flow;

      console.log('👥 Testing in-person delivery flow...');

      // Create a new listing for in-person delivery
      await helpers.signInUser(seller);
      const inPersonListingId = await helpers.createListing({
        ...testData.scenarios.create_listing_shipping_enabled.data,
        price: inPersonData.data.price,
        delivery_methods: ['In-Person']
      });

      // Sign in as buyer
      await helpers.signInUser(buyer);

      // Add wallet balance
      await helpers.addWalletBalance(buyer.uid, inPersonData.data.wallet_credit_used);

      // Create checkout session for in-person delivery
      const checkoutResult = await helpers.createCheckoutSession(
        inPersonListingId,
        true,
        {
          appliedWalletCredit: inPersonData.data.wallet_credit_used,
          deliveryMethod: 'In-Person'
        }
      );

      expect(checkoutResult.success).toBe(true);
      expect(checkoutResult.walletAmountUsed).toBe(inPersonData.data.wallet_credit_used);

      // Simulate payment completion
      await helpers.simulateStripeWebhook(checkoutResult.sessionId, inPersonListingId);

      // Verify order created without shipping requirements
      const order = await helpers.getOrderBySessionId(checkoutResult.sessionId);
      expect(order.deliveryMethod).toBe('In-Person');
      expect(order.shippingAddress).toBeUndefined();

      testData.inPersonOrderId = order.id;
      testData.inPersonSecretCode = order.secretCode;

      console.log(`✅ In-person order created: ${order.id}`);
    });
  });

  describe('🤝 Scenario 07: In-Person Meeting and PIN Entry', () => {
    it('should complete in-person transaction with PIN', async () => {
      const { buyer } = testUsers;

      console.log('🤝 Simulating in-person meeting and PIN entry...');

      // Sign in as buyer
      await helpers.signInUser(buyer);

      // Simulate in-person handoff and PIN entry
      const releaseResult = await helpers.releaseFundsWithCode(
        testData.inPersonOrderId,
        testData.inPersonSecretCode
      );

      expect(releaseResult.success).toBe(true);

      // Verify order completed
      const order = await helpers.getOrder(testData.inPersonOrderId);
      expect(order.status).toBe('completed');
      expect(order.fundsReleased).toBe(true);
      expect(order.deliveryMethod).toBe('In-Person');

      console.log(`✅ In-person transaction completed: ${testData.inPersonOrderId}`);
    });
  });

  describe('↩️ Scenario 08: Mail-in Return Flow', () => {
    it('should handle return request within return window', async () => {
      const { buyer } = testUsers;

      console.log('📦 Testing return request flow...');

      // Create a new order for return testing
      const returnTestOrder = await helpers.createTestOrderForReturns();

      // Sign in as buyer
      await helpers.signInUser(buyer);

      // Request return within return window
      const returnResult = await helpers.requestReturn(
        returnTestOrder.id,
        'Item not as described'
      );

      expect(returnResult.success).toBe(true);

      // Verify return request created
      const order = await helpers.getOrder(returnTestOrder.id);
      expect(order.returnRequested).toBe(true);
      expect(order.returnReason).toBe('Item not as described');
      expect(order.returnRequestedAt).toBeDefined();

      testData.returnOrderId = returnTestOrder.id;

      console.log(`✅ Return requested for order: ${returnTestOrder.id}`);
    });
  });

  describe('📮 Scenario 09: Generate Return Label', () => {
    it('should generate return shipping label via Shippo', async () => {
      console.log('📦 Generating return shipping label...');

      // Generate return label (system function)
      const returnLabelResult = await helpers.generateReturnLabel(testData.returnOrderId);

      expect(returnLabelResult.success).toBe(true);
      expect(returnLabelResult.label.trackingNumber).toBeDefined();
      expect(returnLabelResult.label.labelUrl).toBeDefined();

      // Verify order updated with return label
      const order = await helpers.getOrder(testData.returnOrderId);
      expect(order.returnLabel).toBeDefined();
      expect(order.returnTrackingNumber).toBe(returnLabelResult.label.trackingNumber);

      console.log(`✅ Return label generated: ${returnLabelResult.label.trackingNumber}`);
    });
  });

  describe('💰 Scenario 10: Refund After Return Received', () => {
    it('should process refund when seller confirms return', async () => {
      const { seller } = testUsers;

      console.log('💸 Processing refund after return confirmation...');

      // Sign in as seller
      await helpers.signInUser(seller);

      // Seller confirms return received and approves refund
      const refundResult = await helpers.confirmReturnAndRefund(
        testData.returnOrderId,
        'approve_refund'
      );

      expect(refundResult.success).toBe(true);

      // Verify order refunded
      const order = await helpers.getOrder(testData.returnOrderId);
      expect(order.status).toBe('refunded');
      expect(order.refundedAt).toBeDefined();
      expect(order.refundAmount).toBeDefined();

      console.log(`✅ Refund processed for order: ${testData.returnOrderId}`);
    });
  });

  describe('🔍 Scenario 11: Final Validation', () => {
    it('should validate system integrity and audit logs', async () => {
      console.log('🔍 Running final system validation...');

      // Validate seller payout records
      const payoutValidation = await helpers.validateSellerPayouts();
      expect(payoutValidation.valid).toBe(true);

      // Validate buyer order history
      const orderHistoryValidation = await helpers.validateBuyerOrderHistory();
      expect(orderHistoryValidation.valid).toBe(true);

      // Validate admin audit logs
      const auditValidation = await helpers.validateAdminAuditLogs();
      expect(auditValidation.valid).toBe(true);
      expect(auditValidation.pinLogsSecure).toBe(true);

      // Validate commission calculations
      const commissionValidation = await helpers.validateCommissionCalculations();
      expect(commissionValidation.valid).toBe(true);

      console.log('✅ System integrity validation passed');
    });
  });

  describe('⏰ Auto-Release System Tests', () => {
    it('should auto-release funds after 3 days', async () => {
      console.log('⏰ Testing auto-release system...');

      // Create test order and simulate 3-day passage
      const autoReleaseOrder = await helpers.createTestOrderForAutoRelease();

      // Simulate time passage (3+ days)
      await helpers.simulateTimePassage(autoReleaseOrder.id, 4); // 4 days

      // Trigger auto-release function
      const autoReleaseResult = await helpers.triggerAutoRelease();

      expect(autoReleaseResult.processed).toBeGreaterThan(0);

      // Verify order auto-released
      const order = await helpers.getOrder(autoReleaseOrder.id);
      expect(order.status).toBe('completed');
      expect(order.fundsReleased).toBe(true);
      expect(order.autoReleased).toBe(true);

      console.log(`✅ Auto-release system working correctly`);
    });
  });

  describe('🚨 Error Handling Tests', () => {
    it('should handle invalid PIN attempts securely', async () => {
      const { buyer } = testUsers;

      console.log('🔐 Testing invalid PIN security...');

      // Create test order
      const testOrder = await helpers.createTestOrderForPinTesting();

      // Sign in as buyer
      await helpers.signInUser(buyer);

      // Try invalid PIN
      try {
        await helpers.releaseFundsWithCode(testOrder.id, '000000');
        expect.fail('Should have thrown error for invalid PIN');
      } catch (error) {
        expect(error.message).toContain('Invalid');
      }

      // Verify order status unchanged
      const order = await helpers.getOrder(testOrder.id);
      expect(order.status).not.toBe('completed');
      expect(order.fundsReleased).toBe(false);

      console.log('✅ Invalid PIN security working correctly');
    });

    it('should handle wallet insufficient funds gracefully', async () => {
      const { buyer } = testUsers;

      console.log('💰 Testing insufficient wallet funds...');

      // Sign in as buyer with insufficient funds
      await helpers.signInUser(buyer);

      // Try to checkout with more wallet credit than available
      try {
        await helpers.createCheckoutSession(
          testData.listingId,
          true,
          { appliedWalletCredit: 1000 } // More than available
        );
        expect.fail('Should have thrown error for insufficient funds');
      } catch (error) {
        expect(error.message).toContain('insufficient');
      }

      console.log('✅ Insufficient funds handling working correctly');
    });
  });
});

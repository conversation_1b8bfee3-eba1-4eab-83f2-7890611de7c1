import * as admin from 'firebase-admin';
import * as functions from 'firebase-functions/v1';
import { validateInput } from './validation/schemas';
import { z } from 'zod';

// Secure order status management for Hive Campus

// Valid order statuses
export enum OrderStatus {
  PENDING = 'pending',
  PAYMENT_PROCESSING = 'payment_processing',
  PAYMENT_SUCCEEDED = 'payment_succeeded',
  PAYMENT_FAILED = 'payment_failed',
  CONFIRMED = 'confirmed',
  SHIPPED = 'shipped',
  IN_TRANSIT = 'in_transit',
  DELIVERED = 'delivered',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
  DISPUTED = 'disputed',
  ADMIN_REVIEW = 'admin_review'
}

// Valid status transitions map - defines allowed state changes
export const VALID_STATUS_TRANSITIONS: Record<OrderStatus, OrderStatus[]> = {
  [OrderStatus.PENDING]: [
    OrderStatus.PAYMENT_PROCESSING,
    OrderStatus.CANCELLED
  ],
  [OrderStatus.PAYMENT_PROCESSING]: [
    OrderStatus.PAYMENT_SUCCEEDED,
    OrderStatus.PAYMENT_FAILED,
    OrderStatus.CANCELLED
  ],
  [OrderStatus.PAYMENT_SUCCEEDED]: [
    OrderStatus.CONFIRMED,
    OrderStatus.REFUNDED,
    OrderStatus.CANCELLED
  ],
  [OrderStatus.PAYMENT_FAILED]: [
    OrderStatus.PENDING,
    OrderStatus.CANCELLED
  ],
  [OrderStatus.CONFIRMED]: [
    OrderStatus.SHIPPED,
    OrderStatus.CANCELLED,
    OrderStatus.REFUNDED
  ],
  [OrderStatus.SHIPPED]: [
    OrderStatus.IN_TRANSIT,
    OrderStatus.DELIVERED,
    OrderStatus.DISPUTED
  ],
  [OrderStatus.IN_TRANSIT]: [
    OrderStatus.DELIVERED,
    OrderStatus.DISPUTED
  ],
  [OrderStatus.DELIVERED]: [
    OrderStatus.COMPLETED,
    OrderStatus.DISPUTED
  ],
  [OrderStatus.COMPLETED]: [
    OrderStatus.DISPUTED // Only disputes allowed after completion
  ],
  [OrderStatus.CANCELLED]: [], // Terminal state
  [OrderStatus.REFUNDED]: [], // Terminal state
  [OrderStatus.DISPUTED]: [
    OrderStatus.COMPLETED,
    OrderStatus.REFUNDED,
    OrderStatus.ADMIN_REVIEW
  ],
  [OrderStatus.ADMIN_REVIEW]: [
    OrderStatus.COMPLETED,
    OrderStatus.REFUNDED,
    OrderStatus.CANCELLED
  ]
};

// Order status update schema
const OrderStatusUpdateSchema = z.object({
  orderId: z.string().min(1, 'Order ID is required'),
  newStatus: z.nativeEnum(OrderStatus),
  reason: z.string().max(500).optional(),
  adminOverride: z.boolean().default(false),
  proofOfDelivery: z.object({
    type: z.enum(['fcm_confirmation', 'user_confirmation', 'tracking_update']),
    timestamp: z.string(),
    metadata: z.record(z.unknown()).optional()
  }).optional()
});

interface OrderStatusHistory {
  status: OrderStatus;
  timestamp: admin.firestore.Timestamp;
  updatedBy: string;
  reason?: string;
  adminOverride?: boolean;
  proofOfDelivery?: any;
}

/**
 * Secure order status manager with validation and audit trail
 */
export class SecureOrderStatusManager {
  /**
   * Validate if a status transition is allowed
   */
  static isValidTransition(
    currentStatus: OrderStatus,
    newStatus: OrderStatus,
    isAdminOverride: boolean = false
  ): boolean {
    // Admin override allows any transition except to/from terminal states
    if (isAdminOverride) {
      const terminalStates = [OrderStatus.CANCELLED, OrderStatus.REFUNDED];
      
      // Don't allow admin to override terminal states unless going to admin review
      if (terminalStates.includes(currentStatus) && newStatus !== OrderStatus.ADMIN_REVIEW) {
        return false;
      }
      
      return true;
    }

    // Check if transition is in the allowed list
    const allowedTransitions = VALID_STATUS_TRANSITIONS[currentStatus] || [];
    return allowedTransitions.includes(newStatus);
  }

  /**
   * Update order status with validation and audit trail
   */
  static async updateOrderStatus(
    orderId: string,
    newStatus: OrderStatus,
    updatedBy: string,
    options: {
      reason?: string;
      adminOverride?: boolean;
      proofOfDelivery?: any;
      isSystemUpdate?: boolean;
    } = {}
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Validate input
      const validatedData = validateInput(OrderStatusUpdateSchema, {
        orderId,
        newStatus,
        reason: options.reason,
        adminOverride: options.adminOverride || false,
        proofOfDelivery: options.proofOfDelivery
      });

      const orderRef = admin.firestore().collection('orders').doc(orderId);
      
      const result = await admin.firestore().runTransaction(async (transaction) => {
        const orderDoc = await transaction.get(orderRef);
        
        if (!orderDoc.exists) {
          throw new functions.https.HttpsError('not-found', 'Order not found');
        }

        const orderData = orderDoc.data();
        const currentStatus = orderData?.status as OrderStatus;

        // Skip validation for same status (idempotent)
        if (currentStatus === newStatus) {
          return { success: true, message: 'Status unchanged' };
        }

        // Validate transition
        const isValidTransition = this.isValidTransition(
          currentStatus,
          newStatus,
          validatedData.adminOverride
        );

        if (!isValidTransition) {
          const errorMsg = `Invalid status transition: ${currentStatus} → ${newStatus}`;
          console.error(`❌ ${errorMsg} for order ${orderId}`);
          
          // Log invalid transition attempt
          await this.logStatusAttempt(orderId, currentStatus, newStatus, updatedBy, false, validatedData.reason);
          
          throw new functions.https.HttpsError('failed-precondition', errorMsg);
        }

        // Create status history entry
        const statusHistory: OrderStatusHistory = {
          status: newStatus,
          timestamp: admin.firestore.Timestamp.now(),
          updatedBy,
          reason: validatedData.reason,
          adminOverride: validatedData.adminOverride,
          proofOfDelivery: validatedData.proofOfDelivery
        };

        // Update order with new status and history
        const updateData: any = {
          status: newStatus,
          updatedAt: admin.firestore.Timestamp.now(),
          statusHistory: admin.firestore.FieldValue.arrayUnion(statusHistory)
        };

        // Add specific timestamps for key statuses
        switch (newStatus) {
          case OrderStatus.PAYMENT_SUCCEEDED:
            updateData.paidAt = admin.firestore.Timestamp.now();
            break;
          case OrderStatus.SHIPPED:
            updateData.shippedAt = admin.firestore.Timestamp.now();
            break;
          case OrderStatus.DELIVERED:
            updateData.deliveredAt = admin.firestore.Timestamp.now();
            if (validatedData.proofOfDelivery) {
              updateData.proofOfDelivery = validatedData.proofOfDelivery;
            }
            break;
          case OrderStatus.COMPLETED:
            updateData.completedAt = admin.firestore.Timestamp.now();
            updateData.fundsReleased = true;
            updateData.fundsReleasedAt = admin.firestore.Timestamp.now();
            break;
          case OrderStatus.CANCELLED:
          case OrderStatus.REFUNDED:
            updateData.cancelledAt = admin.firestore.Timestamp.now();
            break;
        }

        transaction.update(orderRef, updateData);

        // Log successful transition
        await this.logStatusAttempt(orderId, currentStatus, newStatus, updatedBy, true, validatedData.reason);

        console.log(`✅ Order ${orderId} status updated: ${currentStatus} → ${newStatus} by ${updatedBy}`);

        return { success: true, message: `Status updated to ${newStatus}` };
      });

      // Trigger post-update actions
      await this.handlePostStatusUpdate(orderId, newStatus, options);

      return result;
    } catch (error) {
      console.error('❌ Error updating order status:', error);
      
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      
      throw new functions.https.HttpsError('internal', 'Failed to update order status');
    }
  }

  /**
   * Log status transition attempts for audit trail
   */
  private static async logStatusAttempt(
    orderId: string,
    fromStatus: OrderStatus,
    toStatus: OrderStatus,
    updatedBy: string,
    success: boolean,
    reason?: string
  ): Promise<void> {
    try {
      await admin.firestore().collection('orderStatusLogs').add({
        orderId,
        fromStatus,
        toStatus,
        updatedBy,
        success,
        reason,
        timestamp: admin.firestore.Timestamp.now(),
        ip: 'system' // Could be enhanced to capture actual IP
      });
    } catch (error) {
      console.error('❌ Error logging status attempt:', error);
      // Don't throw - logging failure shouldn't break the main operation
    }
  }

  /**
   * Handle post-status update actions (notifications, etc.)
   */
  private static async handlePostStatusUpdate(
    orderId: string,
    newStatus: OrderStatus,
    options: any
  ): Promise<void> {
    try {
      // Trigger notifications based on status
      switch (newStatus) {
        case OrderStatus.SHIPPED:
          // Send shipping notification to buyer
          break;
        case OrderStatus.DELIVERED:
          // Start 3-day auto-release timer
          // Send delivery confirmation to buyer
          break;
        case OrderStatus.DISPUTED:
          // Notify admin of dispute
          // Create dispute record
          break;
        case OrderStatus.COMPLETED:
          // Release funds to seller
          // Send completion notifications
          break;
      }
    } catch (error) {
      console.error('❌ Error in post-status update actions:', error);
      // Don't throw - these are non-critical actions
    }
  }

  /**
   * Get order status history
   */
  static async getOrderStatusHistory(orderId: string): Promise<OrderStatusHistory[]> {
    try {
      const orderDoc = await admin.firestore().collection('orders').doc(orderId).get();
      
      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Order not found');
      }

      const orderData = orderDoc.data();
      return orderData?.statusHistory || [];
    } catch (error) {
      console.error('❌ Error getting order status history:', error);
      throw new functions.https.HttpsError('internal', 'Failed to get order history');
    }
  }

  /**
   * Check if order can be transitioned to a specific status
   */
  static canTransitionTo(
    currentStatus: OrderStatus,
    targetStatus: OrderStatus,
    isAdmin: boolean = false
  ): boolean {
    return this.isValidTransition(currentStatus, targetStatus, isAdmin);
  }
}

// Export for use in Firestore rules
export const isValidStatusTransition = (
  currentStatus: string,
  newStatus: string
): boolean => {
  return SecureOrderStatusManager.isValidTransition(
    currentStatus as OrderStatus,
    newStatus as OrderStatus,
    false
  );
};

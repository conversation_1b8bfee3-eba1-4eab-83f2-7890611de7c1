import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { 
  ArrowLeft, 
  Package, 
  Truck, 
  CheckCircle, 
  RotateCcw, 
  MessageCircle,
  Lock,
  AlertCircle,
  Copy,
  Upload,
  Calendar,
  DollarSign,
  User,
  MapPin
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useStripeCheckout } from '../hooks/useStripeCheckout';
import { Order } from '../firebase/types';
import { formatPrice } from '../utils/priceUtils';
import toast from 'react-hot-toast';

const BuyerOrderManagement: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { getOrderById, releaseFundsWithCode, requestReturn, isLoading: checkoutLoading } = useStripeCheckout();

  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [secretCode, setSecretCode] = useState('');
  const [isSubmittingCode, setIsSubmittingCode] = useState(false);
  const [showReturnForm, setShowReturnForm] = useState(false);
  const [returnReason, setReturnReason] = useState('');
  const [returnEvidence, setReturnEvidence] = useState<File | null>(null);
  const [isSubmittingReturn, setIsSubmittingReturn] = useState(false);

  useEffect(() => {
    if (orderId) {
      loadOrder();
    }
  }, [orderId]);

  const loadOrder = async () => {
    if (!orderId) return;

    setIsLoading(true);
    setError(null);

    try {
      const orderData = await getOrderById(orderId);
      if (orderData) {
        // Verify user is the buyer
        if (orderData.buyerId !== currentUser?.uid) {
          setError('You do not have permission to view this order');
          return;
        }
        setOrder(orderData);
      } else {
        setError('Order not found');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load order');
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirmDelivery = async () => {
    if (!orderId || !secretCode.trim()) {
      toast.error('Please enter the secret code');
      return;
    }

    setIsSubmittingCode(true);
    try {
      const success = await releaseFundsWithCode(orderId, secretCode.trim());
      if (success) {
        toast.success('Delivery confirmed! Funds have been released to the seller.');
        await loadOrder(); // Refresh order data
      }
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Failed to confirm delivery');
    } finally {
      setIsSubmittingCode(false);
    }
  };

  const handleReturnRequest = async () => {
    if (!orderId || !returnReason.trim()) {
      toast.error('Please provide a reason for the return');
      return;
    }

    setIsSubmittingReturn(true);
    try {
      await requestReturn(orderId, returnReason, returnEvidence);
      toast.success('Return request submitted successfully');
      setShowReturnForm(false);
      await loadOrder(); // Refresh order data
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Failed to submit return request');
    } finally {
      setIsSubmittingReturn(false);
    }
  };

  const copySecretCode = () => {
    if (order?.secretCode) {
      navigator.clipboard.writeText(order.secretCode);
      toast.success('Secret code copied to clipboard');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending_payment':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'payment_completed':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20';
      case 'delivered':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'completed':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'returned':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20';
      case 'cancelled':
        return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  // Check if returns are allowed (only for mail orders within 3 days)
  const canReturn = order &&
    order.status === 'delivered' &&
    !order.returnRequest &&
    order.deliveryMethod === 'mail' && // Only mail orders can be returned
    order.deliveredAt &&
    (new Date().getTime() - order.deliveredAt.toDate().getTime()) < (3 * 24 * 60 * 60 * 1000); // Within 3 days

  const canConfirmDelivery = order && order.status === 'delivered' && !order.completedAt;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex items-center">
              <AlertCircle className="w-6 h-6 text-red-600 dark:text-red-400 mr-3" />
              <div>
                <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Error</h3>
                <p className="text-red-700 dark:text-red-300">{error || 'Order not found'}</p>
              </div>
            </div>
            <div className="mt-4">
              <button
                onClick={() => navigate('/orders')}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
              >
                Back to Orders
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button
            onClick={() => navigate('/orders')}
            className="flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors mr-4"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Orders
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Order #{order.id.slice(-8)}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">Manage your purchase</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Item Details */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Item Details</h2>
              <div className="flex items-start space-x-4">
                <img
                  src={order.listingImage || '/placeholder-image.jpg'}
                  alt={order.title}
                  className="w-20 h-20 rounded-lg object-cover"
                />
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-2">{order.title}</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{order.description}</p>
                  <div className="flex items-center space-x-4 text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Category: {order.category}</span>
                    <span className="text-gray-600 dark:text-gray-400">
                      Delivery: {order.deliveryMethod === 'mail' ? '📦 Mail' : '🤝 In-Person'}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      {order.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    ${formatPrice(order.finalStripeAmount || order.amount)}
                  </p>
                </div>
              </div>
            </div>

            {/* Tracking Info */}
            {order.trackingInfo && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Tracking Information</h2>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Tracking Number:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{order.trackingInfo.trackingNumber}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Carrier:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{order.trackingInfo.carrier}</span>
                  </div>
                  {order.trackingInfo.trackingUrl && (
                    <div className="pt-2">
                      <a
                        href={order.trackingInfo.trackingUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
                      >
                        <Truck className="w-4 h-4 mr-2" />
                        Track Package
                      </a>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Delivery Confirmation */}
            {canConfirmDelivery && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Confirm Delivery</h2>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Enter the secret code provided by the seller to confirm you received the item and release payment.
                </p>
                <div className="flex space-x-3">
                  <input
                    type="text"
                    value={secretCode}
                    onChange={(e) => setSecretCode(e.target.value)}
                    placeholder="Enter 6-digit secret code"
                    className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    maxLength={6}
                  />
                  <button
                    onClick={handleConfirmDelivery}
                    disabled={isSubmittingCode || !secretCode.trim()}
                    className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
                  >
                    {isSubmittingCode ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    ) : (
                      <CheckCircle className="w-4 h-4 mr-2" />
                    )}
                    Confirm Delivery
                  </button>
                </div>
              </div>
            )}

            {/* Return Request */}
            {order && order.status === 'delivered' && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Return Item</h2>

                {order.deliveryMethod === 'in_person' ? (
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                    <div className="flex items-center">
                      <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" />
                      <p className="text-yellow-700 dark:text-yellow-300">
                        Returns are not available for in-person exchanges.
                      </p>
                    </div>
                  </div>
                ) : !canReturn ? (
                  <div className="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div className="flex items-center">
                      <Clock className="w-5 h-5 text-gray-600 dark:text-gray-400 mr-2" />
                      <p className="text-gray-700 dark:text-gray-300">
                        Return window has expired. Returns must be requested within 3 days of delivery.
                      </p>
                    </div>
                  </div>
                ) : !showReturnForm ? (
                  <div>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                      Not satisfied with your purchase? You can request a return within 3 days of delivery (mail orders only).
                    </p>
                    <button
                      onClick={() => setShowReturnForm(true)}
                      className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center"
                    >
                      <RotateCcw className="w-4 h-4 mr-2" />
                      Request Return
                    </button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Reason for Return
                      </label>
                      <textarea
                        value={returnReason}
                        onChange={(e) => setReturnReason(e.target.value)}
                        placeholder="Please explain why you want to return this item..."
                        rows={4}
                        className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Photo Evidence (Optional)
                      </label>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => setReturnEvidence(e.target.files?.[0] || null)}
                        className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                    <div className="flex space-x-3">
                      <button
                        onClick={() => setShowReturnForm(false)}
                        className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={handleReturnRequest}
                        disabled={isSubmittingReturn || !returnReason.trim()}
                        className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
                      >
                        {isSubmittingReturn ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        ) : (
                          <Upload className="w-4 h-4 mr-2" />
                        )}
                        Submit Return Request
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Secret Code */}
            {order.secretCode && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  <Lock className="w-5 h-5 mr-2" />
                  Secret Code
                </h3>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
                  <p className="text-2xl font-mono font-bold text-gray-900 dark:text-white mb-2">
                    {order.secretCode}
                  </p>
                  <button
                    onClick={copySecretCode}
                    className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors flex items-center justify-center mx-auto"
                  >
                    <Copy className="w-4 h-4 mr-1" />
                    Copy Code
                  </button>
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                  Use this code to confirm delivery and release payment to the seller.
                </p>
              </div>
            )}

            {/* Order Summary */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Order Summary</h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Order Date:</span>
                  <span className="text-gray-900 dark:text-white">
                    {new Date(order.createdAt.toDate()).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Item Price:</span>
                  <span className="text-gray-900 dark:text-white">${formatPrice(order.amount)}</span>
                </div>
                {order.walletAmountUsed && order.walletAmountUsed > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Wallet Credit:</span>
                    <span className="text-green-600 dark:text-green-400">-${formatPrice(order.walletAmountUsed)}</span>
                  </div>
                )}
                <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
                  <div className="flex justify-between font-semibold">
                    <span className="text-gray-900 dark:text-white">Total Paid:</span>
                    <span className="text-gray-900 dark:text-white">
                      ${formatPrice(order.finalStripeAmount || order.amount)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Actions</h3>
              <div className="space-y-3">
                <Link
                  to={`/messages/${order.sellerId}`}
                  className="w-full flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                >
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Chat with Seller
                </Link>
                <Link
                  to="/home"
                  className="w-full flex items-center justify-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Continue Shopping
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BuyerOrderManagement;

import * as admin from 'firebase-admin';
import * as functions from 'firebase-functions/v1';
import axios from 'axios';

// ReeFlex AI moderation integration for Hive Campus

interface ModerationResult {
  isViolation: boolean;
  confidence: number;
  categories: string[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  action: 'allow' | 'flag' | 'block' | 'delete';
  reason?: string;
}

interface ReflexResponse {
  status: string;
  data: {
    is_violation: boolean;
    confidence: number;
    categories: string[];
    severity: string;
    recommended_action: string;
    explanation: string;
  };
}

/**
 * ReeFlex AI moderation system for content and behavior analysis
 */
export class ReflexModerationManager {
  private static readonly REFLEX_API_URL = 'https://api.reflex.ai/v1/moderate';
  private static readonly API_KEY = functions.config().reflex?.api_key || process.env.REFLEX_API_KEY;

  /**
   * Moderate a chat message using ReeFlex AI
   */
  static async moderateMessage(messageId: string): Promise<ModerationResult> {
    try {
      // Get message data
      const messageDoc = await admin.firestore()
        .collection('messages')
        .doc(messageId)
        .get();

      if (!messageDoc.exists) {
        throw new Error('Message not found');
      }

      const messageData = messageDoc.data();
      const text = messageData?.text || '';
      const senderId = messageData?.senderId;

      // Get sender's history for context
      const senderHistory = await this.getSenderHistory(senderId);

      // Call ReeFlex API
      const moderationResult = await this.callReflexAPI({
        content: text,
        type: 'message',
        context: {
          sender_id: senderId,
          message_history: senderHistory.slice(-5), // Last 5 messages for context
          platform: 'hive_campus'
        }
      });

      // Update message with moderation results
      await admin.firestore()
        .collection('messages')
        .doc(messageId)
        .update({
          isModerated: true,
          moderationResult: moderationResult,
          moderatedAt: admin.firestore.Timestamp.now()
        });

      // Take action based on result
      await this.handleModerationAction(messageId, moderationResult);

      console.log(`🤖 Message ${messageId} moderated: ${moderationResult.action} (${moderationResult.confidence}% confidence)`);

      return moderationResult;
    } catch (error) {
      console.error('❌ Error moderating message:', error);
      
      // Default to safe action on error
      return {
        isViolation: false,
        confidence: 0,
        categories: [],
        severity: 'low',
        action: 'allow',
        reason: 'Moderation service unavailable'
      };
    }
  }

  /**
   * Moderate delivery disputes for fake delivery patterns
   */
  static async moderateDeliveryDispute(
    orderId: string,
    disputeType: string,
    description: string
  ): Promise<ModerationResult> {
    try {
      // Get order and seller data
      const orderDoc = await admin.firestore()
        .collection('orders')
        .doc(orderId)
        .get();

      if (!orderDoc.exists) {
        throw new Error('Order not found');
      }

      const orderData = orderDoc.data();
      const sellerId = orderData?.sellerId;

      // Get seller's delivery history
      const sellerHistory = await this.getSellerDeliveryHistory(sellerId);

      // Call ReeFlex API for delivery dispute analysis
      const moderationResult = await this.callReflexAPI({
        content: description,
        type: 'delivery_dispute',
        context: {
          seller_id: sellerId,
          dispute_type: disputeType,
          delivery_history: sellerHistory,
          order_timeline: {
            created_at: orderData.createdAt,
            shipped_at: orderData.shippedAt,
            delivered_at: orderData.deliveredAt
          },
          platform: 'hive_campus'
        }
      });

      // Log moderation result
      await admin.firestore().collection('moderationLogs').add({
        type: 'delivery_dispute',
        orderId,
        sellerId,
        disputeType,
        moderationResult,
        timestamp: admin.firestore.Timestamp.now()
      });

      // Handle high-risk sellers
      if (moderationResult.severity === 'critical' || moderationResult.confidence > 90) {
        await this.flagHighRiskSeller(sellerId, moderationResult);
      }

      console.log(`🚨 Delivery dispute moderated for order ${orderId}: ${moderationResult.action}`);

      return moderationResult;
    } catch (error) {
      console.error('❌ Error moderating delivery dispute:', error);
      
      return {
        isViolation: false,
        confidence: 0,
        categories: [],
        severity: 'low',
        action: 'allow',
        reason: 'Moderation service unavailable'
      };
    }
  }

  /**
   * Analyze user behavior patterns for anomalies
   */
  static async analyzeUserBehavior(userId: string): Promise<ModerationResult> {
    try {
      // Get user's activity data
      const userActivity = await this.getUserActivityData(userId);

      // Call ReeFlex API for behavior analysis
      const moderationResult = await this.callReflexAPI({
        content: JSON.stringify(userActivity),
        type: 'user_behavior',
        context: {
          user_id: userId,
          activity_summary: userActivity,
          platform: 'hive_campus'
        }
      });

      // Log behavior analysis
      await admin.firestore().collection('behaviorAnalysis').add({
        userId,
        moderationResult,
        activityData: userActivity,
        timestamp: admin.firestore.Timestamp.now()
      });

      // Take action for suspicious behavior
      if (moderationResult.isViolation && moderationResult.confidence > 80) {
        await this.handleSuspiciousBehavior(userId, moderationResult);
      }

      return moderationResult;
    } catch (error) {
      console.error('❌ Error analyzing user behavior:', error);
      
      return {
        isViolation: false,
        confidence: 0,
        categories: [],
        severity: 'low',
        action: 'allow'
      };
    }
  }

  /**
   * Call ReeFlex AI API
   */
  private static async callReflexAPI(data: any): Promise<ModerationResult> {
    try {
      if (!this.API_KEY) {
        console.warn('⚠️ ReeFlex API key not configured, using fallback moderation');
        return this.fallbackModeration(data.content);
      }

      const response = await axios.post<ReflexResponse>(
        this.REFLEX_API_URL,
        data,
        {
          headers: {
            'Authorization': `Bearer ${this.API_KEY}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000 // 10 second timeout
        }
      );

      const result = response.data.data;

      return {
        isViolation: result.is_violation,
        confidence: result.confidence,
        categories: result.categories,
        severity: result.severity as any,
        action: result.recommended_action as any,
        reason: result.explanation
      };
    } catch (error) {
      console.error('❌ ReeFlex API call failed:', error);
      return this.fallbackModeration(data.content);
    }
  }

  /**
   * Fallback moderation when ReeFlex is unavailable
   */
  private static fallbackModeration(content: string): ModerationResult {
    const suspiciousPatterns = [
      /\b(scam|fraud|fake|steal|money back|refund guaranteed)\b/i,
      /\b(whatsapp|telegram|signal|contact me at)\b/i,
      /\b(bitcoin|crypto|venmo|cashapp|zelle)\b/i,
      /\b(urgent|limited time|act now|don't miss)\b/i
    ];

    const violations = suspiciousPatterns.filter(pattern => pattern.test(content));
    const isViolation = violations.length > 0;

    return {
      isViolation,
      confidence: isViolation ? 75 : 25,
      categories: isViolation ? ['suspicious_content'] : [],
      severity: isViolation ? 'medium' : 'low',
      action: isViolation ? 'flag' : 'allow',
      reason: isViolation ? 'Suspicious patterns detected' : 'Content appears safe'
    };
  }

  /**
   * Handle moderation actions
   */
  private static async handleModerationAction(
    messageId: string,
    result: ModerationResult
  ): Promise<void> {
    try {
      switch (result.action) {
        case 'delete':
          await admin.firestore()
            .collection('messages')
            .doc(messageId)
            .update({
              isDeleted: true,
              deletedAt: admin.firestore.Timestamp.now(),
              deleteReason: 'Automatic moderation'
            });
          break;

        case 'block':
          // Block the chat
          const messageDoc = await admin.firestore()
            .collection('messages')
            .doc(messageId)
            .get();
          
          if (messageDoc.exists) {
            const chatId = messageDoc.data()?.chatId;
            await admin.firestore()
              .collection('chats')
              .doc(chatId)
              .update({
                isBlocked: true,
                blockedAt: admin.firestore.Timestamp.now(),
                blockReason: 'Automatic moderation'
              });
          }
          break;

        case 'flag':
          // Create admin notification
          await admin.firestore().collection('admin_notifications').add({
            type: 'content_flagged',
            title: 'Message Flagged by AI',
            message: `Message ${messageId} flagged: ${result.categories.join(', ')}`,
            messageId,
            priority: result.severity === 'high' ? 'high' : 'medium',
            createdAt: admin.firestore.Timestamp.now(),
            read: false,
            actionUrl: `/admin/moderation/messages/${messageId}`
          });
          break;

        case 'allow':
        default:
          // No action needed
          break;
      }
    } catch (error) {
      console.error('❌ Error handling moderation action:', error);
    }
  }

  /**
   * Get sender's message history
   */
  private static async getSenderHistory(senderId: string): Promise<any[]> {
    try {
      const messages = await admin.firestore()
        .collection('messages')
        .where('senderId', '==', senderId)
        .orderBy('timestamp', 'desc')
        .limit(10)
        .get();

      return messages.docs.map(doc => ({
        text: doc.data().text,
        timestamp: doc.data().timestamp,
        chatId: doc.data().chatId
      }));
    } catch (error) {
      console.error('❌ Error getting sender history:', error);
      return [];
    }
  }

  /**
   * Get seller's delivery history
   */
  private static async getSellerDeliveryHistory(sellerId: string): Promise<any[]> {
    try {
      const orders = await admin.firestore()
        .collection('orders')
        .where('sellerId', '==', sellerId)
        .where('status', 'in', ['delivered', 'completed', 'disputed'])
        .orderBy('createdAt', 'desc')
        .limit(20)
        .get();

      return orders.docs.map(doc => {
        const data = doc.data();
        return {
          orderId: doc.id,
          status: data.status,
          createdAt: data.createdAt,
          shippedAt: data.shippedAt,
          deliveredAt: data.deliveredAt,
          deliveryTime: data.deliveredAt && data.shippedAt ? 
            data.deliveredAt.toMillis() - data.shippedAt.toMillis() : null
        };
      });
    } catch (error) {
      console.error('❌ Error getting seller delivery history:', error);
      return [];
    }
  }

  /**
   * Get user activity data for behavior analysis
   */
  private static async getUserActivityData(userId: string): Promise<any> {
    try {
      const [orders, messages, listings] = await Promise.all([
        admin.firestore()
          .collection('orders')
          .where('buyerId', '==', userId)
          .orderBy('createdAt', 'desc')
          .limit(10)
          .get(),
        admin.firestore()
          .collection('messages')
          .where('senderId', '==', userId)
          .orderBy('timestamp', 'desc')
          .limit(20)
          .get(),
        admin.firestore()
          .collection('listings')
          .where('sellerId', '==', userId)
          .orderBy('createdAt', 'desc')
          .limit(10)
          .get()
      ]);

      return {
        orderCount: orders.size,
        messageCount: messages.size,
        listingCount: listings.size,
        recentActivity: {
          lastOrder: orders.docs[0]?.data().createdAt,
          lastMessage: messages.docs[0]?.data().timestamp,
          lastListing: listings.docs[0]?.data().createdAt
        }
      };
    } catch (error) {
      console.error('❌ Error getting user activity data:', error);
      return {};
    }
  }

  /**
   * Flag high-risk seller
   */
  private static async flagHighRiskSeller(
    sellerId: string,
    moderationResult: ModerationResult
  ): Promise<void> {
    try {
      await admin.firestore().collection('admin_notifications').add({
        type: 'high_risk_seller',
        title: 'High-Risk Seller Detected',
        message: `Seller ${sellerId} flagged by AI: ${moderationResult.reason}`,
        sellerId,
        moderationResult,
        priority: 'high',
        createdAt: admin.firestore.Timestamp.now(),
        read: false,
        actionUrl: `/admin/users/${sellerId}`
      });

      // Update seller's risk score
      await admin.firestore()
        .collection('users')
        .doc(sellerId)
        .update({
          riskScore: admin.firestore.FieldValue.increment(moderationResult.confidence),
          lastRiskUpdate: admin.firestore.Timestamp.now()
        });
    } catch (error) {
      console.error('❌ Error flagging high-risk seller:', error);
    }
  }

  /**
   * Handle suspicious behavior
   */
  private static async handleSuspiciousBehavior(
    userId: string,
    moderationResult: ModerationResult
  ): Promise<void> {
    try {
      // Create admin notification
      await admin.firestore().collection('admin_notifications').add({
        type: 'suspicious_behavior',
        title: 'Suspicious User Behavior',
        message: `User ${userId} showing suspicious patterns: ${moderationResult.reason}`,
        userId,
        moderationResult,
        priority: 'medium',
        createdAt: admin.firestore.Timestamp.now(),
        read: false,
        actionUrl: `/admin/users/${userId}`
      });

      // If critical, temporarily restrict user
      if (moderationResult.severity === 'critical') {
        await admin.firestore()
          .collection('users')
          .doc(userId)
          .update({
            isRestricted: true,
            restrictedAt: admin.firestore.Timestamp.now(),
            restrictionReason: 'Suspicious behavior detected by AI'
          });
      }
    } catch (error) {
      console.error('❌ Error handling suspicious behavior:', error);
    }
  }
}

// Background function to process moderation queue
export const processModeration = functions.firestore
  .document('moderationQueue/{queueId}')
  .onCreate(async (snap) => {
    const data = snap.data();
    
    if (data.type === 'message' && data.status === 'pending') {
      await ReflexModerationManager.moderateMessage(data.messageId);
      
      // Mark as processed
      await snap.ref.update({
        status: 'processed',
        processedAt: admin.firestore.Timestamp.now()
      });
    }
  });

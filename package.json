{"name": "hive-campus-marketplace", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:clean": "powershell -Command \"Remove-Item -Recurse -Force node_modules\\.vite -ErrorAction SilentlyContinue\" && vite --force", "build": "vite build && node scripts/inject-sw-config.js", "lint": "eslint . --max-warnings=999", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:component": "vitest --config vitest.config.component.ts", "lighthouse": "lighthouse http://localhost:5173 --output=json --output-path=lighthouse-results.json", "perf:build": "npm run build && npm run lighthouse", "perf:test": "node scripts/performance-test.js", "perf:lighthouse": "node scripts/performance-test.js --lighthouse", "perf:bundle": "node scripts/performance-test.js --bundle", "perf:full": "npm run build && npm run perf:test", "security:audit": "node scripts/production-security-audit.cjs", "security:check": "npm audit --audit-level=high", "security:dependencies": "npm audit", "security:fix": "node scripts/fix-security-issues.js", "load:test": "node scripts/load-testing.cjs", "load:test:artillery": "node scripts/load-testing.cjs --artillery", "production:audit": "npm run security:audit && npm run load:test", "cleanup:mock-data": "node scripts/cleanup-mock-data.js", "cleanup:mock-data-cli": "node scripts/cleanup-mock-data-cli.js", "verify:database": "node scripts/verify-database-content.js", "production:deploy": "npm run build && npm run production:audit && firebase deploy", "production:verify": "npm run test && npm run test:e2e && npm run production:audit", "production:setup": "node scripts/setup-production-env.cjs", "production:config": "firebase functions:config:get", "production:validate": "node scripts/validate-functions-env.cjs", "deploy:admin-notifications": "node scripts/deploy-admin-notifications.js", "security:comprehensive": "node scripts/comprehensive-security-audit.js", "security:validate": "node scripts/validate-production-security.js", "security:env": "node scripts/validate-env-vars.js", "load:comprehensive": "node scripts/load-testing.js", "production:checklist": "node scripts/production-deployment-checklist.js", "validate:all": "node scripts/run-comprehensive-validation.js", "validate:security": "npm run test tests/security-validation.test.js", "validate:backend": "npm run test tests/backend-logic-flow.test.js", "validate:frontend": "npm run test tests/frontend-security.test.js", "validate:production": "npm run test tests/production-readiness.test.js", "certification:full": "npm run validate:all && echo 'Hive Campus validation complete!'"}, "dependencies": {"@sentry/react": "^9.33.0", "@stripe/stripe-js": "^7.4.0", "date-fns": "^4.1.0", "firebase": "^11.9.1", "gsap": "^3.13.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.20.1", "three": "^0.178.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@faker-js/faker": "^9.9.0", "@playwright/test": "^1.44.0", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^24.0.12", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/three": "^0.178.1", "@vitejs/plugin-react": "^4.7.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.18", "chrome-launcher": "^1.1.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "glob": "^11.0.3", "globals": "^15.9.0", "jsdom": "^24.1.0", "lighthouse": "^12.0.0", "msw": "^2.3.1", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.4", "typescript-eslint": "^8.3.0", "vite": "^7.0.5", "vite-plugin-pwa": "^1.0.0", "vitest": "^3.2.4"}}
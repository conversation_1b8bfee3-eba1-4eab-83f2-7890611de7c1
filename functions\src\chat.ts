import * as admin from 'firebase-admin';
import * as functions from 'firebase-functions/v1';
import { validateInput } from './validation/schemas';
import { z } from 'zod';

// Secure chat validation system for Hive Campus

// Message validation schema
const MessageSchema = z.object({
  chatId: z.string().min(1, 'Chat ID is required'),
  text: z.string()
    .min(1, 'Message cannot be empty')
    .max(1000, 'Message cannot exceed 1000 characters')
    .regex(/^(?!.*<script).*$/i, 'Message cannot contain script tags'),
  receiverId: z.string().min(1, 'Receiver ID is required'),
  type: z.enum(['text', 'image', 'listing_share']).default('text'),
  imageUrl: z.string().url().optional(),
  listingId: z.string().optional()
});

// Chat creation schema
const ChatCreationSchema = z.object({
  participantId: z.string().min(1, 'Participant ID is required'),
  listingId: z.string().min(1, 'Listing ID is required'),
  initialMessage: z.string()
    .min(1, 'Initial message is required')
    .max(500, 'Initial message cannot exceed 500 characters')
});

// Abuse report schema
const AbuseReportSchema = z.object({
  chatId: z.string().min(1, 'Chat ID is required'),
  messageId: z.string().optional(),
  reason: z.enum(['spam', 'harassment', 'scam', 'inappropriate_content', 'fake_listing', 'other']),
  description: z.string().max(500, 'Description cannot exceed 500 characters').optional()
});

interface ChatData {
  id: string;
  participants: string[];
  listingId: string;
  createdAt: admin.firestore.Timestamp;
  lastMessage?: string;
  lastMessageAt?: admin.firestore.Timestamp;
  isActive: boolean;
  reportCount: number;
  isBlocked: boolean;
}

interface MessageData {
  id: string;
  chatId: string;
  senderId: string;
  receiverId: string;
  text: string;
  type: string;
  imageUrl?: string;
  listingId?: string;
  timestamp: admin.firestore.Timestamp;
  isModerated: boolean;
  moderationFlags?: string[];
  isDeleted: boolean;
}

/**
 * Secure chat management system with participant validation
 */
export class SecureChatManager {
  /**
   * Create a new chat with validation
   */
  static async createChat(
    senderId: string,
    chatData: {
      participantId: string;
      listingId: string;
      initialMessage: string;
    }
  ): Promise<{ success: boolean; chatId: string }> {
    try {
      // Validate input
      const validatedData = validateInput(ChatCreationSchema, chatData);

      // Prevent self-chat
      if (senderId === validatedData.participantId) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'Cannot create chat with yourself'
        );
      }

      // Verify listing exists and get seller info
      const listingDoc = await admin.firestore()
        .collection('listings')
        .doc(validatedData.listingId)
        .get();

      if (!listingDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Listing not found');
      }

      const listingData = listingDoc.data();
      const sellerId = listingData?.sellerId;

      // Verify one participant is the seller
      if (![senderId, validatedData.participantId].includes(sellerId)) {
        throw new functions.https.HttpsError(
          'permission-denied',
          'Chat must include the listing seller'
        );
      }

      // Create deterministic chat ID
      const participants = [senderId, validatedData.participantId].sort();
      const chatId = `${participants[0]}_${participants[1]}_${validatedData.listingId}`;

      // Check if chat already exists
      const existingChat = await admin.firestore()
        .collection('chats')
        .doc(chatId)
        .get();

      if (existingChat.exists) {
        return { success: true, chatId };
      }

      // Create chat and initial message in transaction
      await admin.firestore().runTransaction(async (transaction) => {
        const chatRef = admin.firestore().collection('chats').doc(chatId);
        const messageRef = admin.firestore().collection('messages').doc();

        // Create chat
        const chat: ChatData = {
          id: chatId,
          participants,
          listingId: validatedData.listingId,
          createdAt: admin.firestore.Timestamp.now(),
          lastMessage: validatedData.initialMessage,
          lastMessageAt: admin.firestore.Timestamp.now(),
          isActive: true,
          reportCount: 0,
          isBlocked: false
        };

        transaction.set(chatRef, chat);

        // Create initial message
        const message: MessageData = {
          id: messageRef.id,
          chatId,
          senderId,
          receiverId: validatedData.participantId,
          text: validatedData.initialMessage,
          type: 'text',
          timestamp: admin.firestore.Timestamp.now(),
          isModerated: false,
          isDeleted: false
        };

        transaction.set(messageRef, message);
      });

      console.log(`💬 Chat created: ${chatId} between ${senderId} and ${validatedData.participantId}`);

      return { success: true, chatId };
    } catch (error) {
      console.error('❌ Error creating chat:', error);
      
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      
      throw new functions.https.HttpsError('internal', 'Failed to create chat');
    }
  }

  /**
   * Send message with participant validation
   */
  static async sendMessage(
    senderId: string,
    messageData: {
      chatId: string;
      text: string;
      receiverId: string;
      type?: string;
      imageUrl?: string;
      listingId?: string;
    }
  ): Promise<{ success: boolean; messageId: string }> {
    try {
      // Validate input
      const validatedData = validateInput(MessageSchema, messageData);

      // Verify chat exists and sender is participant
      const chatDoc = await admin.firestore()
        .collection('chats')
        .doc(validatedData.chatId)
        .get();

      if (!chatDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Chat not found');
      }

      const chatData = chatDoc.data() as ChatData;

      // Verify sender is a participant
      if (!chatData.participants.includes(senderId)) {
        throw new functions.https.HttpsError(
          'permission-denied',
          'You are not a participant in this chat'
        );
      }

      // Verify receiver is a participant
      if (!chatData.participants.includes(validatedData.receiverId)) {
        throw new functions.https.HttpsError(
          'permission-denied',
          'Receiver is not a participant in this chat'
        );
      }

      // Check if chat is blocked
      if (chatData.isBlocked) {
        throw new functions.https.HttpsError(
          'permission-denied',
          'This chat has been blocked due to policy violations'
        );
      }

      // Rate limiting check
      const canSend = await this.checkMessageRateLimit(senderId);
      if (!canSend) {
        throw new functions.https.HttpsError(
          'resource-exhausted',
          'Message rate limit exceeded. Please wait before sending another message.'
        );
      }

      // Create message
      const messageRef = admin.firestore().collection('messages').doc();
      const message: MessageData = {
        id: messageRef.id,
        chatId: validatedData.chatId,
        senderId,
        receiverId: validatedData.receiverId,
        text: validatedData.text,
        type: validatedData.type || 'text',
        imageUrl: validatedData.imageUrl,
        listingId: validatedData.listingId,
        timestamp: admin.firestore.Timestamp.now(),
        isModerated: false,
        isDeleted: false
      };

      // Send message and update chat in transaction
      await admin.firestore().runTransaction(async (transaction) => {
        // Create message
        transaction.set(messageRef, message);

        // Update chat last message
        transaction.update(admin.firestore().collection('chats').doc(validatedData.chatId), {
          lastMessage: validatedData.text,
          lastMessageAt: admin.firestore.Timestamp.now()
        });
      });

      // Queue message for moderation (background function)
      await this.queueMessageForModeration(messageRef.id);

      console.log(`📨 Message sent: ${messageRef.id} in chat ${validatedData.chatId}`);

      return { success: true, messageId: messageRef.id };
    } catch (error) {
      console.error('❌ Error sending message:', error);
      
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      
      throw new functions.https.HttpsError('internal', 'Failed to send message');
    }
  }

  /**
   * Report abuse in chat
   */
  static async reportAbuse(
    reporterId: string,
    reportData: {
      chatId: string;
      messageId?: string;
      reason: string;
      description?: string;
    }
  ): Promise<{ success: boolean; reportId: string }> {
    try {
      // Validate input
      const validatedData = validateInput(AbuseReportSchema, reportData);

      // Verify reporter is a participant in the chat
      const chatDoc = await admin.firestore()
        .collection('chats')
        .doc(validatedData.chatId)
        .get();

      if (!chatDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Chat not found');
      }

      const chatData = chatDoc.data() as ChatData;
      if (!chatData.participants.includes(reporterId)) {
        throw new functions.https.HttpsError(
          'permission-denied',
          'You are not a participant in this chat'
        );
      }

      // Create abuse report
      const reportRef = admin.firestore().collection('abuseReports').doc();
      const report = {
        id: reportRef.id,
        chatId: validatedData.chatId,
        messageId: validatedData.messageId,
        reporterId,
        reason: validatedData.reason,
        description: validatedData.description,
        status: 'open',
        createdAt: admin.firestore.Timestamp.now(),
        reviewedAt: null,
        reviewedBy: null,
        action: null
      };

      await reportRef.set(report);

      // Update chat report count
      await admin.firestore()
        .collection('chats')
        .doc(validatedData.chatId)
        .update({
          reportCount: admin.firestore.FieldValue.increment(1)
        });

      // Auto-block chat if too many reports
      if (chatData.reportCount >= 2) {
        await this.blockChat(validatedData.chatId, 'Automatic block due to multiple reports');
      }

      // Notify admin
      await this.notifyAdminOfAbuseReport(report);

      console.log(`🚨 Abuse report created: ${reportRef.id} for chat ${validatedData.chatId}`);

      return { success: true, reportId: reportRef.id };
    } catch (error) {
      console.error('❌ Error reporting abuse:', error);
      
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      
      throw new functions.https.HttpsError('internal', 'Failed to report abuse');
    }
  }

  /**
   * Check message rate limiting
   */
  private static async checkMessageRateLimit(userId: string): Promise<boolean> {
    try {
      const rateLimitRef = admin.firestore()
        .collection('rateLimits')
        .doc(`message_${userId}`);

      const result = await admin.firestore().runTransaction(async (transaction) => {
        const doc = await transaction.get(rateLimitRef);
        const now = admin.firestore.Timestamp.now();
        const windowMs = 60 * 1000; // 1 minute window
        const maxMessages = 10; // Max 10 messages per minute

        if (!doc.exists) {
          transaction.set(rateLimitRef, {
            count: 1,
            windowStart: now
          });
          return true;
        }

        const data = doc.data();
        const windowStart = data?.windowStart?.toMillis() || 0;
        const currentWindow = now.toMillis() - windowMs;

        // Reset if window has passed
        if (windowStart < currentWindow) {
          transaction.set(rateLimitRef, {
            count: 1,
            windowStart: now
          });
          return true;
        }

        // Check if limit exceeded
        if (data?.count >= maxMessages) {
          return false;
        }

        // Increment count
        transaction.update(rateLimitRef, {
          count: admin.firestore.FieldValue.increment(1)
        });

        return true;
      });

      return result;
    } catch (error) {
      console.error('❌ Error checking message rate limit:', error);
      return false; // Fail secure
    }
  }

  /**
   * Queue message for moderation
   */
  private static async queueMessageForModeration(messageId: string): Promise<void> {
    try {
      await admin.firestore().collection('moderationQueue').add({
        messageId,
        type: 'message',
        status: 'pending',
        createdAt: admin.firestore.Timestamp.now()
      });
    } catch (error) {
      console.error('❌ Error queuing message for moderation:', error);
    }
  }

  /**
   * Block a chat
   */
  private static async blockChat(chatId: string, reason: string): Promise<void> {
    try {
      await admin.firestore()
        .collection('chats')
        .doc(chatId)
        .update({
          isBlocked: true,
          blockedAt: admin.firestore.Timestamp.now(),
          blockReason: reason
        });

      console.log(`🚫 Chat blocked: ${chatId} - ${reason}`);
    } catch (error) {
      console.error('❌ Error blocking chat:', error);
    }
  }

  /**
   * Notify admin of abuse report
   */
  private static async notifyAdminOfAbuseReport(report: any): Promise<void> {
    try {
      await admin.firestore().collection('admin_notifications').add({
        type: 'abuse_report',
        title: `Chat Abuse Report: ${report.reason}`,
        message: `Chat ${report.chatId} has been reported for ${report.reason}`,
        reportId: report.id,
        chatId: report.chatId,
        priority: 'medium',
        createdAt: admin.firestore.Timestamp.now(),
        read: false,
        actionUrl: `/admin/reports/${report.id}`
      });
    } catch (error) {
      console.error('❌ Error notifying admin of abuse report:', error);
    }
  }
}

"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleCreateCheckoutSession = handleCreateCheckoutSession;
const admin = __importStar(require("firebase-admin"));
const functions = __importStar(require("firebase-functions/v1"));
const Stripe = require('stripe');
// Simple wallet utility functions
async function getWalletBalance(userId) {
    try {
        const walletDoc = await admin.firestore().collection('wallets').doc(userId).get();
        if (!walletDoc.exists)
            return 0;
        const wallet = walletDoc.data();
        return (wallet === null || wallet === void 0 ? void 0 : wallet.balance) || 0;
    }
    catch (error) {
        console.error('Error getting wallet balance:', error);
        return 0;
    }
}
function validateWalletUsage(requestedAmount, availableBalance, totalCost) {
    if (requestedAmount < 0) {
        return { isValid: false, error: 'Wallet amount cannot be negative' };
    }
    if (requestedAmount > availableBalance) {
        return { isValid: false, error: `Insufficient wallet balance. Available: $${availableBalance}, Requested: $${requestedAmount}` };
    }
    if (requestedAmount > totalCost) {
        return { isValid: false, error: `Wallet amount cannot exceed total cost. Total: $${totalCost}, Requested: $${requestedAmount}` };
    }
    return { isValid: true };
}
async function deductFromWallet(userId, amount, description, orderId, transaction) {
    const walletRef = admin.firestore().collection('wallets').doc(userId);
    const walletTransaction = {
        id: admin.firestore().collection('temp').doc().id,
        type: 'debit',
        amount,
        description,
        source: 'purchase_deduction',
        orderId,
        createdAt: admin.firestore.Timestamp.now()
    };
    const updateData = {
        balance: admin.firestore.FieldValue.increment(-amount),
        history: admin.firestore.FieldValue.arrayUnion(walletTransaction),
        lastUpdated: admin.firestore.Timestamp.now()
    };
    if (transaction) {
        transaction.update(walletRef, updateData);
    }
    else {
        await walletRef.update(updateData);
    }
}
// Enhanced create checkout session function
async function handleCreateCheckoutSession(req, res) {
    var _a, _b, _c, _d;
    try {
        console.log('💳 Creating checkout session with wallet support...');
        // Check authentication
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            res.status(401).json({ error: 'Unauthorized: Missing authentication token' });
            return;
        }
        const token = authHeader.split('Bearer ')[1];
        const decodedToken = await admin.auth().verifyIdToken(token);
        const buyerId = decodedToken.uid;
        // Get request data
        const { listingId, quantity = 1, useWalletBalance = false, orderDetails } = req.body;
        const walletBalanceUsed = (orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.appliedWalletCredit) || 0;
        if (!listingId) {
            res.status(400).json({ error: 'Listing ID is required' });
            return;
        }
        console.log(`🛒 Creating checkout for listing: ${listingId}, wallet: $${walletBalanceUsed}`);
        // Get listing details
        const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
        if (!listingDoc.exists) {
            res.status(404).json({ error: 'Listing not found' });
            return;
        }
        const listing = listingDoc.data();
        if (!listing) {
            res.status(404).json({ error: 'Listing data not found' });
            return;
        }
        // Get seller ID
        const sellerId = listing.ownerId || listing.userId;
        if (!sellerId) {
            res.status(400).json({ error: 'Invalid listing data: missing seller information' });
            return;
        }
        // Calculate amounts
        const itemPrice = listing.price;
        const shippingCost = (orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.shippingFee) || 0;
        const totalBeforeWallet = itemPrice * quantity + shippingCost;
        const commissionRate = 0.05;
        const commissionAmount = (itemPrice * quantity) * commissionRate;
        const cashbackAmount = (itemPrice * quantity) * 0.02;
        // Validate wallet usage
        let actualWalletUsed = 0;
        if (walletBalanceUsed > 0) {
            const userWalletBalance = await getWalletBalance(buyerId);
            console.log(`💳 Wallet balance: $${userWalletBalance}, requested: $${walletBalanceUsed}`);
            const validation = validateWalletUsage(walletBalanceUsed, userWalletBalance, totalBeforeWallet);
            if (!validation.isValid) {
                res.status(400).json({ error: validation.error });
                return;
            }
            actualWalletUsed = walletBalanceUsed;
        }
        // Calculate final amount
        const finalAmount = Math.max(0, totalBeforeWallet - actualWalletUsed);
        console.log(`🧮 Final: $${totalBeforeWallet} - $${actualWalletUsed} = $${finalAmount}`);
        // Create order
        const orderRef = admin.firestore().collection('orders').doc();
        const orderId = orderRef.id;
        // Generate secret code for all orders (needed for delivery confirmation)
        const secretCode = Math.floor(100000 + Math.random() * 900000).toString();

        const orderData = {
            id: orderId,
            listingId,
            listingTitle: listing.title,
            buyerId,
            sellerId,
            quantity,
            itemPrice,
            shippingCost,
            totalBeforeWallet,
            walletAmountUsed: actualWalletUsed,
            finalStripeAmount: finalAmount,
            commissionAmount,
            cashbackAmount,
            status: finalAmount === 0 ? 'payment_succeeded' : 'pending_payment',
            secretCode: finalAmount === 0 ? secretCode : null, // Only set for completed orders
            paymentCompletedAt: finalAmount === 0 ? admin.firestore.Timestamp.now() : null,
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        };
        // Use transaction for atomicity
        await admin.firestore().runTransaction(async (transaction) => {
            transaction.set(orderRef, orderData);
            if (actualWalletUsed > 0) {
                await deductFromWallet(buyerId, actualWalletUsed, `Purchase: ${listing.title}`, orderId, transaction);
            }
        });
        // Handle zero-amount orders
        if (finalAmount === 0) {
            console.log(`🎉 Order fully covered by wallet balance!`);

            // Update listing to sold (since payment is complete)
            try {
                await admin.firestore().collection('listings').doc(listingId).update({
                    status: 'sold',
                    soldAt: admin.firestore.Timestamp.now(),
                    updatedAt: admin.firestore.Timestamp.now()
                });
                console.log(`✅ Listing ${listingId} marked as sold (wallet payment)`);
            } catch (error) {
                console.error(`❌ Error updating listing status:`, error);
                // Don't fail the order if listing update fails
            }

            res.status(200).json({
                success: true,
                paidWithWallet: true,
                walletAmountUsed: actualWalletUsed,
                orderId,
                secretCode,
                message: `Order paid successfully using $${actualWalletUsed} from wallet balance`
            });
            return;
        }
        // Create Stripe session for remaining amount
        const stripeConfig = functions.config();
        const stripeApiKey = ((_a = stripeConfig.stripe) === null || _a === void 0 ? void 0 : _a.api_key) || ((_b = stripeConfig.stripe) === null || _b === void 0 ? void 0 : _b.secret_key);
        if (!stripeApiKey) {
            res.status(500).json({ error: 'Stripe API key not configured' });
            return;
        }
        const stripe = new Stripe(stripeApiKey, { apiVersion: '2025-05-28.basil' });
        const session = await stripe.checkout.sessions.create({
            payment_method_types: ['card'],
            line_items: [{
                    price_data: {
                        currency: 'usd',
                        product_data: {
                            name: `${listing.title}${actualWalletUsed > 0 ? ` (after $${actualWalletUsed} wallet credit)` : ''}`,
                            description: listing.description || 'Hive Campus item',
                        },
                        unit_amount: Math.round(finalAmount * 100),
                    },
                    quantity: 1,
                }],
            mode: 'payment',
            success_url: `${((_c = stripeConfig.app) === null || _c === void 0 ? void 0 : _c.url) || 'https://h1c1-798a8.web.app'}/order-success?session_id={CHECKOUT_SESSION_ID}&order_id=${orderId}`,
            cancel_url: `${((_d = stripeConfig.app) === null || _d === void 0 ? void 0 : _d.url) || 'https://h1c1-798a8.web.app'}/listing/${listingId}`,
            metadata: {
                orderId,
                listingId,
                buyerId,
                sellerId,
                walletAmountUsed: actualWalletUsed.toString(),
                originalTotal: totalBeforeWallet.toString(),
                finalAmount: finalAmount.toString(),
            },
        });
        // Update order with session ID
        await orderRef.update({
            stripeSessionId: session.id,
            updatedAt: admin.firestore.Timestamp.now()
        });
        res.status(200).json({
            success: true,
            sessionId: session.id,
            sessionUrl: session.url,
            orderId,
            walletAmountUsed: actualWalletUsed,
            finalStripeAmount: finalAmount,
            originalTotal: totalBeforeWallet
        });
        console.log(`✅ Checkout session created: ${session.id} for $${finalAmount}`);
    }
    catch (error) {
        console.error('❌ Error creating checkout session:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        res.status(500).json({ error: errorMessage });
    }
}

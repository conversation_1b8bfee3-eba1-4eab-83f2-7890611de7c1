import * as admin from 'firebase-admin';
import * as functions from 'firebase-functions/v1';
import { validateInput } from './validation/schemas';
import { z } from 'zod';
import { SecureOrderStatusManager, OrderStatus } from './order-status';

// Secure dispute handling system for Hive Campus

export enum DisputeType {
  FAKE_DELIVERY = 'fake_delivery',
  ITEM_NOT_RECEIVED = 'item_not_received',
  ITEM_NOT_AS_DESCRIBED = 'item_not_as_described',
  DAMAGED_ITEM = 'damaged_item',
  WRONG_ITEM = 'wrong_item',
  SELLER_UNRESPONSIVE = 'seller_unresponsive',
  OTHER = 'other'
}

export enum DisputeStatus {
  OPEN = 'open',
  UNDER_REVIEW = 'under_review',
  RESOLVED_BUYER_FAVOR = 'resolved_buyer_favor',
  RESOLVED_SELLER_FAVOR = 'resolved_seller_favor',
  RESOLVED_PARTIAL = 'resolved_partial',
  CLOSED = 'closed'
}

// Dispute creation schema
const CreateDisputeSchema = z.object({
  orderId: z.string().min(1, 'Order ID is required'),
  type: z.nativeEnum(DisputeType),
  description: z.string().min(10, 'Description must be at least 10 characters').max(1000),
  evidence: z.array(z.object({
    type: z.enum(['image', 'document', 'screenshot']),
    url: z.string().url(),
    description: z.string().max(200).optional()
  })).max(10).optional(),
  requestedResolution: z.enum(['full_refund', 'partial_refund', 'replacement', 'store_credit'])
});

// Dispute resolution schema
const ResolveDisputeSchema = z.object({
  disputeId: z.string().min(1, 'Dispute ID is required'),
  resolution: z.nativeEnum(DisputeStatus),
  adminNotes: z.string().max(1000),
  refundAmount: z.number().min(0).optional(),
  actionTaken: z.string().max(500)
});

interface DisputeData {
  id: string;
  orderId: string;
  buyerId: string;
  sellerId: string;
  type: DisputeType;
  status: DisputeStatus;
  description: string;
  evidence?: any[];
  requestedResolution: string;
  createdAt: admin.firestore.Timestamp;
  updatedAt: admin.firestore.Timestamp;
  resolvedAt?: admin.firestore.Timestamp;
  resolvedBy?: string;
  adminNotes?: string;
  refundAmount?: number;
  actionTaken?: string;
}

/**
 * Secure dispute management system
 */
export class SecureDisputeManager {
  /**
   * Create a new dispute with validation
   */
  static async createDispute(
    buyerId: string,
    disputeData: {
      orderId: string;
      type: DisputeType;
      description: string;
      evidence?: any[];
      requestedResolution: string;
    }
  ): Promise<{ success: boolean; disputeId: string }> {
    try {
      // Validate input
      const validatedData = validateInput(CreateDisputeSchema, disputeData);

      // Check if order exists and buyer is authorized
      const orderRef = admin.firestore().collection('orders').doc(validatedData.orderId);
      const orderDoc = await orderRef.get();

      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Order not found');
      }

      const orderData = orderDoc.data();
      
      // Verify buyer authorization
      if (orderData?.buyerId !== buyerId) {
        throw new functions.https.HttpsError('permission-denied', 'Not authorized to dispute this order');
      }

      // Check if dispute window is still open (72 hours after delivery)
      const deliveredAt = orderData?.deliveredAt;
      if (deliveredAt) {
        const disputeWindowMs = 72 * 60 * 60 * 1000; // 72 hours
        const now = Date.now();
        const deliveryTime = deliveredAt.toMillis();
        
        if (now - deliveryTime > disputeWindowMs) {
          throw new functions.https.HttpsError(
            'failed-precondition',
            'Dispute window has expired (72 hours after delivery)'
          );
        }
      }

      // Check for existing disputes
      const existingDispute = await admin.firestore()
        .collection('disputes')
        .where('orderId', '==', validatedData.orderId)
        .where('status', 'in', [DisputeStatus.OPEN, DisputeStatus.UNDER_REVIEW])
        .limit(1)
        .get();

      if (!existingDispute.empty) {
        throw new functions.https.HttpsError(
          'already-exists',
          'An active dispute already exists for this order'
        );
      }

      // Create dispute record
      const disputeRef = admin.firestore().collection('disputes').doc();
      const dispute: DisputeData = {
        id: disputeRef.id,
        orderId: validatedData.orderId,
        buyerId,
        sellerId: orderData.sellerId,
        type: validatedData.type,
        status: DisputeStatus.OPEN,
        description: validatedData.description,
        evidence: validatedData.evidence || [],
        requestedResolution: validatedData.requestedResolution,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      };

      // Use transaction to create dispute and update order
      await admin.firestore().runTransaction(async (transaction) => {
        // Create dispute
        transaction.set(disputeRef, dispute);
        
        // Update order status to disputed
        await SecureOrderStatusManager.updateOrderStatus(
          validatedData.orderId,
          OrderStatus.DISPUTED,
          buyerId,
          {
            reason: `Dispute created: ${validatedData.type}`,
            isSystemUpdate: true
          }
        );
      });

      // Send admin notification
      await this.notifyAdminOfDispute(dispute);

      // Log dispute creation
      console.log(`🚨 Dispute created: ${disputeRef.id} for order ${validatedData.orderId} by ${buyerId}`);

      return { success: true, disputeId: disputeRef.id };
    } catch (error) {
      console.error('❌ Error creating dispute:', error);
      
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      
      throw new functions.https.HttpsError('internal', 'Failed to create dispute');
    }
  }

  /**
   * Resolve a dispute (admin only)
   */
  static async resolveDispute(
    adminId: string,
    resolutionData: {
      disputeId: string;
      resolution: DisputeStatus;
      adminNotes: string;
      refundAmount?: number;
      actionTaken: string;
    }
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Validate input
      const validatedData = validateInput(ResolveDisputeSchema, resolutionData);

      const disputeRef = admin.firestore().collection('disputes').doc(validatedData.disputeId);
      
      const result = await admin.firestore().runTransaction(async (transaction) => {
        const disputeDoc = await transaction.get(disputeRef);
        
        if (!disputeDoc.exists) {
          throw new functions.https.HttpsError('not-found', 'Dispute not found');
        }

        const disputeData = disputeDoc.data() as DisputeData;
        
        // Check if dispute is still open
        if (![DisputeStatus.OPEN, DisputeStatus.UNDER_REVIEW].includes(disputeData.status)) {
          throw new functions.https.HttpsError(
            'failed-precondition',
            'Dispute is already resolved'
          );
        }

        // Update dispute with resolution
        const updateData = {
          status: validatedData.resolution,
          resolvedAt: admin.firestore.Timestamp.now(),
          resolvedBy: adminId,
          adminNotes: validatedData.adminNotes,
          actionTaken: validatedData.actionTaken,
          updatedAt: admin.firestore.Timestamp.now()
        };

        if (validatedData.refundAmount !== undefined) {
          updateData.refundAmount = validatedData.refundAmount;
        }

        transaction.update(disputeRef, updateData);

        // Update order status based on resolution
        let newOrderStatus: OrderStatus;
        switch (validatedData.resolution) {
          case DisputeStatus.RESOLVED_BUYER_FAVOR:
            newOrderStatus = OrderStatus.REFUNDED;
            break;
          case DisputeStatus.RESOLVED_SELLER_FAVOR:
            newOrderStatus = OrderStatus.COMPLETED;
            break;
          case DisputeStatus.RESOLVED_PARTIAL:
            newOrderStatus = OrderStatus.COMPLETED;
            break;
          default:
            newOrderStatus = OrderStatus.ADMIN_REVIEW;
        }

        // Update order status
        await SecureOrderStatusManager.updateOrderStatus(
          disputeData.orderId,
          newOrderStatus,
          adminId,
          {
            reason: `Dispute resolved: ${validatedData.resolution}`,
            adminOverride: true,
            isSystemUpdate: true
          }
        );

        return { success: true, message: `Dispute resolved: ${validatedData.resolution}` };
      });

      // Handle post-resolution actions
      await this.handleDisputeResolution(validatedData.disputeId, validatedData.resolution, validatedData.refundAmount);

      console.log(`✅ Dispute ${validatedData.disputeId} resolved by admin ${adminId}: ${validatedData.resolution}`);

      return result;
    } catch (error) {
      console.error('❌ Error resolving dispute:', error);
      
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      
      throw new functions.https.HttpsError('internal', 'Failed to resolve dispute');
    }
  }

  /**
   * Get disputes for admin dashboard
   */
  static async getDisputesForAdmin(
    status?: DisputeStatus,
    limit: number = 20
  ): Promise<DisputeData[]> {
    try {
      let query = admin.firestore()
        .collection('disputes')
        .orderBy('createdAt', 'desc')
        .limit(limit);

      if (status) {
        query = query.where('status', '==', status);
      }

      const snapshot = await query.get();
      return snapshot.docs.map(doc => doc.data() as DisputeData);
    } catch (error) {
      console.error('❌ Error getting disputes for admin:', error);
      throw new functions.https.HttpsError('internal', 'Failed to get disputes');
    }
  }

  /**
   * Check for fake delivery patterns using AI
   */
  static async checkForFakeDeliveryPatterns(orderId: string): Promise<boolean> {
    try {
      // Get recent orders from the same seller
      const orderDoc = await admin.firestore().collection('orders').doc(orderId).get();
      if (!orderDoc.exists) return false;

      const orderData = orderDoc.data();
      const sellerId = orderData?.sellerId;

      // Get seller's recent orders
      const recentOrders = await admin.firestore()
        .collection('orders')
        .where('sellerId', '==', sellerId)
        .where('status', '==', OrderStatus.DELIVERED)
        .orderBy('deliveredAt', 'desc')
        .limit(10)
        .get();

      // Check for suspicious patterns
      const orders = recentOrders.docs.map(doc => doc.data());
      
      // Pattern 1: Too many quick deliveries
      const quickDeliveries = orders.filter(order => {
        const shippedAt = order.shippedAt?.toMillis();
        const deliveredAt = order.deliveredAt?.toMillis();
        return deliveredAt && shippedAt && (deliveredAt - shippedAt) < (2 * 60 * 60 * 1000); // Less than 2 hours
      });

      if (quickDeliveries.length >= 3) {
        console.warn(`🚨 Suspicious delivery pattern detected for seller ${sellerId}: ${quickDeliveries.length} quick deliveries`);
        return true;
      }

      // Pattern 2: High dispute rate
      const disputes = await admin.firestore()
        .collection('disputes')
        .where('sellerId', '==', sellerId)
        .where('type', '==', DisputeType.FAKE_DELIVERY)
        .get();

      if (disputes.size >= 2) {
        console.warn(`🚨 High fake delivery dispute rate for seller ${sellerId}: ${disputes.size} disputes`);
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ Error checking fake delivery patterns:', error);
      return false;
    }
  }

  /**
   * Notify admin of new dispute
   */
  private static async notifyAdminOfDispute(dispute: DisputeData): Promise<void> {
    try {
      await admin.firestore().collection('admin_notifications').add({
        type: 'dispute_created',
        title: `New Dispute: ${dispute.type}`,
        message: `Order ${dispute.orderId} has been disputed by buyer`,
        disputeId: dispute.id,
        orderId: dispute.orderId,
        priority: 'high',
        createdAt: admin.firestore.Timestamp.now(),
        read: false,
        actionUrl: `/admin/disputes/${dispute.id}`
      });
    } catch (error) {
      console.error('❌ Error notifying admin of dispute:', error);
    }
  }

  /**
   * Handle post-resolution actions
   */
  private static async handleDisputeResolution(
    disputeId: string,
    resolution: DisputeStatus,
    refundAmount?: number
  ): Promise<void> {
    try {
      // Send notifications to buyer and seller
      // Process refunds if applicable
      // Update seller reputation if needed
      
      console.log(`📧 Processing post-resolution actions for dispute ${disputeId}: ${resolution}`);
    } catch (error) {
      console.error('❌ Error in post-resolution actions:', error);
    }
  }
}

// Export dispute types and statuses for use in other modules
export { DisputeType, DisputeStatus };

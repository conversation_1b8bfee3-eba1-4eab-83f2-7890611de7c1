import * as functions from 'firebase-functions/v1';
import Stripe from 'stripe';

// Secure Stripe webhook utilities for Hive Campus

/**
 * Secure Stripe webhook handler with signature verification
 */
export class SecureStripeWebhook {
  private static stripe = new Stripe(
    functions.config().stripe?.api_key || process.env.STRIPE_API_KEY || '',
    { apiVersion: '2025-05-28.basil' }
  );

  /**
   * Verify Stripe webhook signature and construct event
   */
  static verifyWebhookSignature(
    rawBody: Buffer | string,
    signature: string,
    endpointSecret: string
  ): Stripe.Event {
    try {
      // Verify the webhook signature
      const event = this.stripe.webhooks.constructEvent(
        rawBody,
        signature,
        endpointSecret
      );

      console.log(`✅ Webhook signature verified for event: ${event.type}`);
      return event;
    } catch (error) {
      console.error('❌ Webhook signature verification failed:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('timestamp')) {
          throw new functions.https.HttpsError(
            'invalid-argument',
            'Webhook timestamp too old'
          );
        } else if (error.message.includes('signature')) {
          throw new functions.https.HttpsError(
            'unauthenticated',
            'Invalid webhook signature'
          );
        }
      }
      
      throw new functions.https.HttpsError(
        'unauthenticated',
        'Webhook verification failed'
      );
    }
  }

  /**
   * Validate webhook event type and structure
   */
  static validateWebhookEvent(event: Stripe.Event): boolean {
    // List of allowed event types for security
    const allowedEventTypes = [
      'checkout.session.completed',
      'payment_intent.succeeded',
      'payment_intent.payment_failed',
      'account.updated',
      'invoice.payment_succeeded',
      'invoice.payment_failed'
    ];

    if (!allowedEventTypes.includes(event.type)) {
      console.warn(`⚠️ Received unexpected webhook event type: ${event.type}`);
      return false;
    }

    // Validate event structure
    if (!event.id || !event.created || !event.data) {
      console.error('❌ Invalid webhook event structure');
      return false;
    }

    // Check event age (reject events older than 5 minutes for security)
    const eventAge = Date.now() / 1000 - event.created;
    if (eventAge > 300) { // 5 minutes
      console.warn(`⚠️ Webhook event too old: ${eventAge} seconds`);
      return false;
    }

    return true;
  }

  /**
   * Extract and validate metadata from Stripe objects
   */
  static validateMetadata(stripeObject: any): Record<string, string> {
    const metadata = stripeObject.metadata || {};
    
    // Validate required metadata fields
    const requiredFields = ['orderId', 'buyerId', 'sellerId'];
    const missingFields = requiredFields.filter(field => !metadata[field]);
    
    if (missingFields.length > 0) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        `Missing required metadata fields: ${missingFields.join(', ')}`
      );
    }

    // Validate metadata values
    Object.entries(metadata).forEach(([key, value]) => {
      if (typeof value !== 'string' || value.length === 0) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          `Invalid metadata value for ${key}`
        );
      }
      
      // Sanitize metadata values
      if (value.length > 500) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          `Metadata value too long for ${key}`
        );
      }
    });

    return metadata;
  }

  /**
   * Validate payment amounts for security
   */
  static validatePaymentAmount(amount: number, expectedAmount?: number): boolean {
    // Check for reasonable amount limits
    if (amount < 50 || amount > 1000000) { // $0.50 to $10,000
      console.error(`❌ Payment amount outside allowed range: $${amount / 100}`);
      return false;
    }

    // If expected amount is provided, verify it matches
    if (expectedAmount !== undefined) {
      const tolerance = 1; // 1 cent tolerance
      if (Math.abs(amount - expectedAmount) > tolerance) {
        console.error(`❌ Payment amount mismatch: expected $${expectedAmount / 100}, got $${amount / 100}`);
        return false;
      }
    }

    return true;
  }

  /**
   * Log webhook events for security monitoring
   */
  static logWebhookEvent(event: Stripe.Event, status: 'success' | 'error', error?: string): void {
    const logData = {
      eventId: event.id,
      eventType: event.type,
      status,
      timestamp: new Date().toISOString(),
      error: error || null,
      metadata: event.data.object.metadata || {}
    };

    console.log(`📊 Webhook Event Log:`, JSON.stringify(logData, null, 2));

    // In production, you might want to store this in Firestore for audit trails
    // await admin.firestore().collection('webhookLogs').add(logData);
  }

  /**
   * Handle webhook processing with error recovery
   */
  static async processWebhookSafely<T>(
    event: Stripe.Event,
    processor: (event: Stripe.Event) => Promise<T>
  ): Promise<T> {
    try {
      const result = await processor(event);
      this.logWebhookEvent(event, 'success');
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logWebhookEvent(event, 'error', errorMessage);
      
      // Re-throw the error for proper HTTP response
      throw error;
    }
  }

  /**
   * Validate Stripe Connect account for security
   */
  static validateConnectAccount(account: Stripe.Account): boolean {
    // Check if account is properly onboarded
    if (!account.details_submitted || !account.charges_enabled) {
      console.warn(`⚠️ Connect account not fully onboarded: ${account.id}`);
      return false;
    }

    // Check for suspicious account patterns
    if (account.created && Date.now() / 1000 - account.created < 3600) {
      console.warn(`⚠️ Very new Connect account: ${account.id}`);
      // Don't reject, but log for monitoring
    }

    return true;
  }

  /**
   * Create secure checkout session with validation
   */
  static async createSecureCheckoutSession(params: {
    amount: number;
    currency: string;
    metadata: Record<string, string>;
    successUrl: string;
    cancelUrl: string;
  }): Promise<Stripe.Checkout.Session> {
    // Validate parameters
    if (!this.validatePaymentAmount(params.amount)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Invalid payment amount'
      );
    }

    // Validate URLs
    const allowedDomains = [
      'https://h1c1-798a8.web.app',
      'https://h1c1-798a8.firebaseapp.com',
      'https://hivecampus.app',
      'https://www.hivecampus.app'
    ];

    const isValidUrl = (url: string) => 
      allowedDomains.some(domain => url.startsWith(domain));

    if (!isValidUrl(params.successUrl) || !isValidUrl(params.cancelUrl)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Invalid redirect URLs'
      );
    }

    try {
      const session = await this.stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [{
          price_data: {
            currency: params.currency,
            product_data: {
              name: 'Hive Campus Purchase'
            },
            unit_amount: params.amount
          },
          quantity: 1
        }],
        mode: 'payment',
        success_url: params.successUrl,
        cancel_url: params.cancelUrl,
        metadata: params.metadata,
        // Security settings
        payment_intent_data: {
          metadata: params.metadata
        },
        expires_at: Math.floor(Date.now() / 1000) + (30 * 60) // 30 minutes
      });

      console.log(`✅ Secure checkout session created: ${session.id}`);
      return session;
    } catch (error) {
      console.error('❌ Error creating checkout session:', error);
      throw new functions.https.HttpsError(
        'internal',
        'Failed to create checkout session'
      );
    }
  }
}

// Export webhook endpoint secret getter
export const getWebhookSecret = (): string => {
  const secret = functions.config().stripe?.webhook_secret || process.env.STRIPE_WEBHOOK_SECRET;
  
  if (!secret) {
    throw new functions.https.HttpsError(
      'failed-precondition',
      'Stripe webhook secret not configured'
    );
  }
  
  return secret;
};

// Export Stripe instance for other uses
export const getSecureStripeInstance = (): Stripe => {
  const apiKey = functions.config().stripe?.api_key || process.env.STRIPE_API_KEY;
  
  if (!apiKey) {
    throw new functions.https.HttpsError(
      'failed-precondition',
      'Stripe API key not configured'
    );
  }
  
  return new Stripe(apiKey, { apiVersion: '2025-05-28.basil' });
};
